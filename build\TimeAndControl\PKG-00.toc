('O:\\python\\shangweiji_test\\build\\TimeAndControl\\TimeAndControl.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'O:\\python\\shangweiji_test\\build\\TimeAndControl\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'O:\\python\\shangweiji_test\\build\\TimeAndControl\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'O:\\python\\shangweiji_test\\build\\TimeAndControl\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'O:\\python\\shangweiji_test\\build\\TimeAndControl\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'O:\\python\\shangweiji_test\\build\\TimeAndControl\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'O:\\python\\shangweiji_test\\build\\TimeAndControl\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt6',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt6.py',
   'PYSOURCE'),
  ('pyi_rth_pyqtgraph_multiprocess',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pyqtgraph_multiprocess.py',
   'PYSOURCE'),
  ('main', 'O:\\python\\shangweiji_test\\main.py', 'PYSOURCE'),
  ('python312.dll',
   'I:\\Program Files\\python\\python12\\python312.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'BINARY'),
  ('unicodedata.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('select.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp312-win_amd64.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\random\\_philox.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp312-win_amd64.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\random\\_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp312-win_amd64.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\random\\_common.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('PyQt6\\QtTest.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\QtTest.pyd',
   'EXTENSION'),
  ('PyQt6\\QtOpenGLWidgets.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\QtOpenGLWidgets.pyd',
   'EXTENSION'),
  ('PyQt6\\QtOpenGL.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\QtOpenGL.pyd',
   'EXTENSION'),
  ('PyQt6\\QtSvg.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\QtSvg.pyd',
   'EXTENSION'),
  ('PyQt6\\sip.cp312-win_amd64.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\sip.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt6\\QtGui.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt6\\QtCore.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\QtCore.pyd',
   'EXTENSION'),
  ('crc16\\_crc16.cp312-win_amd64.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\crc16\\_crc16.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt6\\QtWidgets.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\QtWidgets.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'I:\\Program Files\\python\\python12\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'I:\\Program Files\\python\\python12\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'I:\\Program Files\\python\\python12\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'I:\\Program Files\\python\\python12\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'I:\\Program Files\\python\\python12\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('python3.dll', 'I:\\Program Files\\python\\python12\\python3.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Test.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Test.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6OpenGLWidgets.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6OpenGLWidgets.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6OpenGL.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6OpenGL.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'BINARY'),
  ('ucrtbase.dll', 'F:\\ProgramData\\miniconda3\\ucrtbase.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('pyqtgraph-0.13.7.dist-info\\INSTALLER',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph-0.13.7.dist-info\\INSTALLER',
   'DATA'),
  ('pyqtgraph-0.13.7.dist-info\\LICENSE.txt',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph-0.13.7.dist-info\\LICENSE.txt',
   'DATA'),
  ('pyqtgraph-0.13.7.dist-info\\METADATA',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph-0.13.7.dist-info\\METADATA',
   'DATA'),
  ('pyqtgraph-0.13.7.dist-info\\RECORD',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph-0.13.7.dist-info\\RECORD',
   'DATA'),
  ('pyqtgraph-0.13.7.dist-info\\REQUESTED',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph-0.13.7.dist-info\\REQUESTED',
   'DATA'),
  ('pyqtgraph-0.13.7.dist-info\\WHEEL',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph-0.13.7.dist-info\\WHEEL',
   'DATA'),
  ('pyqtgraph-0.13.7.dist-info\\top_level.txt',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph-0.13.7.dist-info\\top_level.txt',
   'DATA'),
  ('pyqtgraph\\GraphicsScene\\GraphicsScene.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\GraphicsScene.py',
   'DATA'),
  ('pyqtgraph\\GraphicsScene\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\__init__.py',
   'DATA'),
  ('pyqtgraph\\GraphicsScene\\exportDialog.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\exportDialog.py',
   'DATA'),
  ('pyqtgraph\\GraphicsScene\\exportDialogTemplate_generic.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\exportDialogTemplate_generic.py',
   'DATA'),
  ('pyqtgraph\\GraphicsScene\\mouseEvents.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\mouseEvents.py',
   'DATA'),
  ('pyqtgraph\\PlotData.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\PlotData.py',
   'DATA'),
  ('pyqtgraph\\Point.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Point.py',
   'DATA'),
  ('pyqtgraph\\Qt\\QtCore\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtCore\\__init__.py',
   'DATA'),
  ('pyqtgraph\\Qt\\QtCore\\__init__.pyi',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtCore\\__init__.pyi',
   'DATA'),
  ('pyqtgraph\\Qt\\QtGui\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtGui\\__init__.py',
   'DATA'),
  ('pyqtgraph\\Qt\\QtGui\\__init__.pyi',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtGui\\__init__.pyi',
   'DATA'),
  ('pyqtgraph\\Qt\\QtSvg.pyi',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtSvg.pyi',
   'DATA'),
  ('pyqtgraph\\Qt\\QtTest.pyi',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtTest.pyi',
   'DATA'),
  ('pyqtgraph\\Qt\\QtWidgets\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtWidgets\\__init__.py',
   'DATA'),
  ('pyqtgraph\\Qt\\QtWidgets\\__init__.pyi',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtWidgets\\__init__.pyi',
   'DATA'),
  ('pyqtgraph\\Qt\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\__init__.py',
   'DATA'),
  ('pyqtgraph\\Qt\\__init__.pyi',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\__init__.pyi',
   'DATA'),
  ('pyqtgraph\\Qt\\compat\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\compat\\__init__.py',
   'DATA'),
  ('pyqtgraph\\Qt\\internals.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\internals.py',
   'DATA'),
  ('pyqtgraph\\SRTTransform.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\SRTTransform.py',
   'DATA'),
  ('pyqtgraph\\SRTTransform3D.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\SRTTransform3D.py',
   'DATA'),
  ('pyqtgraph\\SignalProxy.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\SignalProxy.py',
   'DATA'),
  ('pyqtgraph\\ThreadsafeTimer.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\ThreadsafeTimer.py',
   'DATA'),
  ('pyqtgraph\\Transform3D.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Transform3D.py',
   'DATA'),
  ('pyqtgraph\\Vector.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Vector.py',
   'DATA'),
  ('pyqtgraph\\WidgetGroup.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\WidgetGroup.py',
   'DATA'),
  ('pyqtgraph\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\__init__.py',
   'DATA'),
  ('pyqtgraph\\canvas\\Canvas.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\Canvas.py',
   'DATA'),
  ('pyqtgraph\\canvas\\CanvasItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\CanvasItem.py',
   'DATA'),
  ('pyqtgraph\\canvas\\CanvasManager.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\CanvasManager.py',
   'DATA'),
  ('pyqtgraph\\canvas\\CanvasTemplate_generic.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\CanvasTemplate_generic.py',
   'DATA'),
  ('pyqtgraph\\canvas\\TransformGuiTemplate_generic.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\TransformGuiTemplate_generic.py',
   'DATA'),
  ('pyqtgraph\\canvas\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\__init__.py',
   'DATA'),
  ('pyqtgraph\\colormap.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colormap.py',
   'DATA'),
  ('pyqtgraph\\colors\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\__init__.py',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CC-BY license - applies to CET color map data.txt',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CC-BY '
   'license - applies to CET color map data.txt',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CC0 legal code - applies to virids, magma, '
   'plasma, inferno and cividis.txt',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CC0 '
   'legal code - applies to virids, magma, plasma, inferno and cividis.txt',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C1s.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C1s.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C2.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C2s.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C2s.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C3.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C3.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C3s.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C3s.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C4.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C4.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C4s.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C4s.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C5.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C5.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C5s.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C5s.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C6.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C6.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C6s.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C6s.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C7.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C7.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C7s.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C7s.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBC1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBC1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBC2.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBC2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBD1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBD1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBL1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBL1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBL2.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBL2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBTC1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBTC1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBTC2.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBTC2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBTD1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBTD1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBTL1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBTL1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBTL2.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBTL2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D10.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D10.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D11.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D11.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D12.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D12.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D13.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D13.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D1A.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D1A.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D2.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D3.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D3.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D4.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D4.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D6.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D6.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D7.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D7.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D8.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D8.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D9.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D9.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-I1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-I1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-I2.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-I2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-I3.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-I3.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L10.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L10.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L11.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L11.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L12.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L12.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L13.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L13.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L14.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L14.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L15.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L15.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L16.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L16.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L17.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L17.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L18.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L18.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L19.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L19.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L2.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L3.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L3.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L4.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L4.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L5.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L5.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L6.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L6.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L7.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L7.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L8.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L8.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L9.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L9.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-R1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-R1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-R2.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-R2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-R3.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-R3.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-R4.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-R4.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\PAL-relaxed.hex',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\PAL-relaxed.hex',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\PAL-relaxed_bright.hex',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\PAL-relaxed_bright.hex',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\cividis.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\cividis.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\inferno.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\inferno.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\magma.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\magma.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\plasma.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\plasma.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\turbo.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\turbo.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\viridis.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\viridis.csv',
   'DATA'),
  ('pyqtgraph\\colors\\palette.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\palette.py',
   'DATA'),
  ('pyqtgraph\\configfile.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\configfile.py',
   'DATA'),
  ('pyqtgraph\\console\\CmdInput.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\CmdInput.py',
   'DATA'),
  ('pyqtgraph\\console\\Console.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\Console.py',
   'DATA'),
  ('pyqtgraph\\console\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\__init__.py',
   'DATA'),
  ('pyqtgraph\\console\\exception_widget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\exception_widget.py',
   'DATA'),
  ('pyqtgraph\\console\\repl_widget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\repl_widget.py',
   'DATA'),
  ('pyqtgraph\\console\\stackwidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\stackwidget.py',
   'DATA'),
  ('pyqtgraph\\debug.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\debug.py',
   'DATA'),
  ('pyqtgraph\\dockarea\\Container.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\dockarea\\Container.py',
   'DATA'),
  ('pyqtgraph\\dockarea\\Dock.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\dockarea\\Dock.py',
   'DATA'),
  ('pyqtgraph\\dockarea\\DockArea.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\dockarea\\DockArea.py',
   'DATA'),
  ('pyqtgraph\\dockarea\\DockDrop.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\dockarea\\DockDrop.py',
   'DATA'),
  ('pyqtgraph\\dockarea\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\dockarea\\__init__.py',
   'DATA'),
  ('pyqtgraph\\examples\\Arrow.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\Arrow.py',
   'DATA'),
  ('pyqtgraph\\examples\\AxisItem_label_overlap.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\AxisItem_label_overlap.py',
   'DATA'),
  ('pyqtgraph\\examples\\BarGraphItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\BarGraphItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\CLIexample.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\CLIexample.py',
   'DATA'),
  ('pyqtgraph\\examples\\ColorBarItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ColorBarItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\ColorButton.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ColorButton.py',
   'DATA'),
  ('pyqtgraph\\examples\\ColorGradientPlots.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ColorGradientPlots.py',
   'DATA'),
  ('pyqtgraph\\examples\\ConsoleWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ConsoleWidget.py',
   'DATA'),
  ('pyqtgraph\\examples\\CustomGraphItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\CustomGraphItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\DataSlicing.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\DataSlicing.py',
   'DATA'),
  ('pyqtgraph\\examples\\DataTreeWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\DataTreeWidget.py',
   'DATA'),
  ('pyqtgraph\\examples\\DateAxisItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\DateAxisItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\DateAxisItem_QtDesigner.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\DateAxisItem_QtDesigner.py',
   'DATA'),
  ('pyqtgraph\\examples\\DiffTreeWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\DiffTreeWidget.py',
   'DATA'),
  ('pyqtgraph\\examples\\Draw.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\Draw.py',
   'DATA'),
  ('pyqtgraph\\examples\\ErrorBarItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ErrorBarItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\ExampleApp.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ExampleApp.py',
   'DATA'),
  ('pyqtgraph\\examples\\FillBetweenItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\FillBetweenItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\Flowchart.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\Flowchart.py',
   'DATA'),
  ('pyqtgraph\\examples\\FlowchartCustomNode.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\FlowchartCustomNode.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLBarGraphItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLBarGraphItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLGradientLegendItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLGradientLegendItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLGraphItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLGraphItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLImageItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLImageItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLIsosurface.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLIsosurface.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLLinePlotItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLLinePlotItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLMeshItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLMeshItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLPainterItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLPainterItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLScatterPlotItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLScatterPlotItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLSurfacePlot.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLSurfacePlot.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLTextItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLTextItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLViewWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLViewWidget.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLVolumeItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLVolumeItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLshaders.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLshaders.py',
   'DATA'),
  ('pyqtgraph\\examples\\GradientEditor.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GradientEditor.py',
   'DATA'),
  ('pyqtgraph\\examples\\GradientWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GradientWidget.py',
   'DATA'),
  ('pyqtgraph\\examples\\GraphItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GraphItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GraphicsLayout.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GraphicsLayout.py',
   'DATA'),
  ('pyqtgraph\\examples\\GraphicsScene.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GraphicsScene.py',
   'DATA'),
  ('pyqtgraph\\examples\\HistogramLUT.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\HistogramLUT.py',
   'DATA'),
  ('pyqtgraph\\examples\\ImageItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ImageItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\ImageView.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ImageView.py',
   'DATA'),
  ('pyqtgraph\\examples\\InfiniteLine.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\InfiniteLine.py',
   'DATA'),
  ('pyqtgraph\\examples\\InteractiveParameter.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\InteractiveParameter.py',
   'DATA'),
  ('pyqtgraph\\examples\\JoystickButton.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\JoystickButton.py',
   'DATA'),
  ('pyqtgraph\\examples\\Legend.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\Legend.py',
   'DATA'),
  ('pyqtgraph\\examples\\LogPlotTest.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\LogPlotTest.py',
   'DATA'),
  ('pyqtgraph\\examples\\MatrixDisplayExample.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\MatrixDisplayExample.py',
   'DATA'),
  ('pyqtgraph\\examples\\MouseSelection.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\MouseSelection.py',
   'DATA'),
  ('pyqtgraph\\examples\\MultiDataPlot.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\MultiDataPlot.py',
   'DATA'),
  ('pyqtgraph\\examples\\MultiPlotSpeedTest.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\MultiPlotSpeedTest.py',
   'DATA'),
  ('pyqtgraph\\examples\\MultiplePlotAxes.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\MultiplePlotAxes.py',
   'DATA'),
  ('pyqtgraph\\examples\\NonUniformImage.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\NonUniformImage.py',
   'DATA'),
  ('pyqtgraph\\examples\\PColorMeshItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\PColorMeshItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\PanningPlot.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\PanningPlot.py',
   'DATA'),
  ('pyqtgraph\\examples\\PlotAutoRange.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\PlotAutoRange.py',
   'DATA'),
  ('pyqtgraph\\examples\\PlotSpeedTest.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\PlotSpeedTest.py',
   'DATA'),
  ('pyqtgraph\\examples\\PlotWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\PlotWidget.py',
   'DATA'),
  ('pyqtgraph\\examples\\Plotting.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\Plotting.py',
   'DATA'),
  ('pyqtgraph\\examples\\ProgressDialog.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ProgressDialog.py',
   'DATA'),
  ('pyqtgraph\\examples\\ROIExamples.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ROIExamples.py',
   'DATA'),
  ('pyqtgraph\\examples\\ROItypes.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ROItypes.py',
   'DATA'),
  ('pyqtgraph\\examples\\RemoteGraphicsView.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\RemoteGraphicsView.py',
   'DATA'),
  ('pyqtgraph\\examples\\RemoteSpeedTest.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\RemoteSpeedTest.py',
   'DATA'),
  ('pyqtgraph\\examples\\RunExampleApp.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\RunExampleApp.py',
   'DATA'),
  ('pyqtgraph\\examples\\ScaleBar.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ScaleBar.py',
   'DATA'),
  ('pyqtgraph\\examples\\ScatterPlot.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ScatterPlot.py',
   'DATA'),
  ('pyqtgraph\\examples\\ScatterPlotSpeedTest.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ScatterPlotSpeedTest.py',
   'DATA'),
  ('pyqtgraph\\examples\\ScatterPlotWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ScatterPlotWidget.py',
   'DATA'),
  ('pyqtgraph\\examples\\SimplePlot.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\SimplePlot.py',
   'DATA'),
  ('pyqtgraph\\examples\\SpinBox.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\SpinBox.py',
   'DATA'),
  ('pyqtgraph\\examples\\Symbols.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\Symbols.py',
   'DATA'),
  ('pyqtgraph\\examples\\TableWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\TableWidget.py',
   'DATA'),
  ('pyqtgraph\\examples\\TreeWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\TreeWidget.py',
   'DATA'),
  ('pyqtgraph\\examples\\VideoSpeedTest.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\VideoSpeedTest.py',
   'DATA'),
  ('pyqtgraph\\examples\\VideoTemplate_generic.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\VideoTemplate_generic.py',
   'DATA'),
  ('pyqtgraph\\examples\\ViewBox.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ViewBox.py',
   'DATA'),
  ('pyqtgraph\\examples\\ViewBoxFeatures.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ViewBoxFeatures.py',
   'DATA'),
  ('pyqtgraph\\examples\\ViewLimits.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ViewLimits.py',
   'DATA'),
  ('pyqtgraph\\examples\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\__init__.py',
   'DATA'),
  ('pyqtgraph\\examples\\__main__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\__main__.py',
   'DATA'),
  ('pyqtgraph\\examples\\_buildParamTypes.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\_buildParamTypes.py',
   'DATA'),
  ('pyqtgraph\\examples\\_paramtreecfg.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\_paramtreecfg.py',
   'DATA'),
  ('pyqtgraph\\examples\\beeswarm.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\beeswarm.py',
   'DATA'),
  ('pyqtgraph\\examples\\colorMaps.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\colorMaps.py',
   'DATA'),
  ('pyqtgraph\\examples\\colorMapsLinearized.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\colorMapsLinearized.py',
   'DATA'),
  ('pyqtgraph\\examples\\console_exception_inspection.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\console_exception_inspection.py',
   'DATA'),
  ('pyqtgraph\\examples\\contextMenu.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\contextMenu.py',
   'DATA'),
  ('pyqtgraph\\examples\\crosshair.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\crosshair.py',
   'DATA'),
  ('pyqtgraph\\examples\\customGraphicsItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\customGraphicsItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\customPlot.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\customPlot.py',
   'DATA'),
  ('pyqtgraph\\examples\\cx_freeze\\plotTest.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\cx_freeze\\plotTest.py',
   'DATA'),
  ('pyqtgraph\\examples\\cx_freeze\\setup.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\cx_freeze\\setup.py',
   'DATA'),
  ('pyqtgraph\\examples\\designerExample.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\designerExample.py',
   'DATA'),
  ('pyqtgraph\\examples\\dockarea.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\dockarea.py',
   'DATA'),
  ('pyqtgraph\\examples\\exampleLoaderTemplate_generic.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\exampleLoaderTemplate_generic.py',
   'DATA'),
  ('pyqtgraph\\examples\\fractal.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\fractal.py',
   'DATA'),
  ('pyqtgraph\\examples\\glow.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\glow.py',
   'DATA'),
  ('pyqtgraph\\examples\\hdf5.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\hdf5.py',
   'DATA'),
  ('pyqtgraph\\examples\\histogram.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\histogram.py',
   'DATA'),
  ('pyqtgraph\\examples\\imageAnalysis.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\imageAnalysis.py',
   'DATA'),
  ('pyqtgraph\\examples\\infiniteline_performance.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\infiniteline_performance.py',
   'DATA'),
  ('pyqtgraph\\examples\\isocurve.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\isocurve.py',
   'DATA'),
  ('pyqtgraph\\examples\\jupyter_console_example.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\jupyter_console_example.py',
   'DATA'),
  ('pyqtgraph\\examples\\linkedViews.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\linkedViews.py',
   'DATA'),
  ('pyqtgraph\\examples\\logAxis.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\logAxis.py',
   'DATA'),
  ('pyqtgraph\\examples\\multiplePlotSpeedTest.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\multiplePlotSpeedTest.py',
   'DATA'),
  ('pyqtgraph\\examples\\multiprocess.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\multiprocess.py',
   'DATA'),
  ('pyqtgraph\\examples\\optics\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\optics\\__init__.py',
   'DATA'),
  ('pyqtgraph\\examples\\optics\\pyoptic.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\optics\\pyoptic.py',
   'DATA'),
  ('pyqtgraph\\examples\\optics\\schott_glasses.csv.gz',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\optics\\schott_glasses.csv.gz',
   'DATA'),
  ('pyqtgraph\\examples\\optics_demos.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\optics_demos.py',
   'DATA'),
  ('pyqtgraph\\examples\\parallelize.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\parallelize.py',
   'DATA'),
  ('pyqtgraph\\examples\\parametertree.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\parametertree.py',
   'DATA'),
  ('pyqtgraph\\examples\\py2exe\\plotTest.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\py2exe\\plotTest.py',
   'DATA'),
  ('pyqtgraph\\examples\\py2exe\\setup.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\py2exe\\setup.py',
   'DATA'),
  ('pyqtgraph\\examples\\relativity\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\relativity\\__init__.py',
   'DATA'),
  ('pyqtgraph\\examples\\relativity\\presets\\Grid Expansion.cfg',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\relativity\\presets\\Grid '
   'Expansion.cfg',
   'DATA'),
  ('pyqtgraph\\examples\\relativity\\presets\\Twin Paradox (grid).cfg',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\relativity\\presets\\Twin '
   'Paradox (grid).cfg',
   'DATA'),
  ('pyqtgraph\\examples\\relativity\\presets\\Twin Paradox.cfg',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\relativity\\presets\\Twin '
   'Paradox.cfg',
   'DATA'),
  ('pyqtgraph\\examples\\relativity\\relativity.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\relativity\\relativity.py',
   'DATA'),
  ('pyqtgraph\\examples\\relativity_demo.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\relativity_demo.py',
   'DATA'),
  ('pyqtgraph\\examples\\scrollingPlots.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\scrollingPlots.py',
   'DATA'),
  ('pyqtgraph\\examples\\syntax.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\syntax.py',
   'DATA'),
  ('pyqtgraph\\examples\\template.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\template.py',
   'DATA'),
  ('pyqtgraph\\examples\\test_examples.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\test_examples.py',
   'DATA'),
  ('pyqtgraph\\examples\\text.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\text.py',
   'DATA'),
  ('pyqtgraph\\examples\\utils.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\utils.py',
   'DATA'),
  ('pyqtgraph\\examples\\verlet_chain\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\verlet_chain\\__init__.py',
   'DATA'),
  ('pyqtgraph\\examples\\verlet_chain\\chain.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\verlet_chain\\chain.py',
   'DATA'),
  ('pyqtgraph\\examples\\verlet_chain\\relax.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\verlet_chain\\relax.py',
   'DATA'),
  ('pyqtgraph\\examples\\verlet_chain_demo.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\verlet_chain_demo.py',
   'DATA'),
  ('pyqtgraph\\exceptionHandling.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exceptionHandling.py',
   'DATA'),
  ('pyqtgraph\\exporters\\CSVExporter.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\CSVExporter.py',
   'DATA'),
  ('pyqtgraph\\exporters\\Exporter.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\Exporter.py',
   'DATA'),
  ('pyqtgraph\\exporters\\HDF5Exporter.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\HDF5Exporter.py',
   'DATA'),
  ('pyqtgraph\\exporters\\ImageExporter.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\ImageExporter.py',
   'DATA'),
  ('pyqtgraph\\exporters\\Matplotlib.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\Matplotlib.py',
   'DATA'),
  ('pyqtgraph\\exporters\\PrintExporter.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\PrintExporter.py',
   'DATA'),
  ('pyqtgraph\\exporters\\SVGExporter.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\SVGExporter.py',
   'DATA'),
  ('pyqtgraph\\exporters\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\__init__.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\Flowchart.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\Flowchart.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\FlowchartCtrlTemplate_generic.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\FlowchartCtrlTemplate_generic.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\FlowchartGraphicsView.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\FlowchartGraphicsView.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\Node.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\Node.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\NodeLibrary.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\NodeLibrary.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\Terminal.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\Terminal.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\__init__.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\library\\Data.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\Data.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\library\\Display.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\Display.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\library\\Filters.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\Filters.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\library\\Operators.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\Operators.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\library\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\__init__.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\library\\common.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\common.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\library\\functions.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\functions.py',
   'DATA'),
  ('pyqtgraph\\frozenSupport.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\frozenSupport.py',
   'DATA'),
  ('pyqtgraph\\functions.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\functions.py',
   'DATA'),
  ('pyqtgraph\\functions_numba.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\functions_numba.py',
   'DATA'),
  ('pyqtgraph\\functions_qimage.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\functions_qimage.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ArrowItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ArrowItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\AxisItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\AxisItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\BarGraphItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\BarGraphItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ButtonItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ButtonItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ColorBarItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ColorBarItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\CurvePoint.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\CurvePoint.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\DateAxisItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\DateAxisItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ErrorBarItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ErrorBarItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\FillBetweenItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\FillBetweenItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\GradientEditorItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GradientEditorItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\GradientLegend.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GradientLegend.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\GradientPresets.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GradientPresets.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\GraphItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\GraphicsItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\GraphicsLayout.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsLayout.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\GraphicsObject.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsObject.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\GraphicsWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsWidget.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\GraphicsWidgetAnchor.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsWidgetAnchor.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\GridItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GridItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\HistogramLUTItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\HistogramLUTItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ImageItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ImageItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\InfiniteLine.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\InfiniteLine.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\IsocurveItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\IsocurveItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ItemGroup.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ItemGroup.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\LabelItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\LabelItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\LegendItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\LegendItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\LinearRegionItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\LinearRegionItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\MultiPlotItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\MultiPlotItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\NonUniformImage.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\NonUniformImage.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\PColorMeshItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PColorMeshItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\PlotCurveItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotCurveItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\PlotDataItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotDataItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\PlotItem\\PlotItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotItem\\PlotItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\PlotItem\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotItem\\__init__.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\PlotItem\\plotConfigTemplate_generic.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotItem\\plotConfigTemplate_generic.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ROI.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ROI.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ScaleBar.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ScaleBar.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ScatterPlotItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ScatterPlotItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\TargetItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\TargetItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\TextItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\TextItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\UIGraphicsItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\UIGraphicsItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\VTickGroup.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\VTickGroup.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ViewBox\\ViewBox.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\ViewBox.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ViewBox\\ViewBoxMenu.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\ViewBoxMenu.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ViewBox\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\__init__.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ViewBox\\axisCtrlTemplate_generic.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\axisCtrlTemplate_generic.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\__init__.py',
   'DATA'),
  ('pyqtgraph\\icons\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\__init__.py',
   'DATA'),
  ('pyqtgraph\\icons\\auto.png',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\auto.png',
   'DATA'),
  ('pyqtgraph\\icons\\ctrl.png',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\ctrl.png',
   'DATA'),
  ('pyqtgraph\\icons\\default.png',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\default.png',
   'DATA'),
  ('pyqtgraph\\icons\\icons.svg',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\icons.svg',
   'DATA'),
  ('pyqtgraph\\icons\\invisibleEye.svg',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\invisibleEye.svg',
   'DATA'),
  ('pyqtgraph\\icons\\lock.png',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\lock.png',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\peegee.svg',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\peegee.svg',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\peegee_128px.png',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\peegee_128px.png',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\<EMAIL>',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\<EMAIL>',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\peegee_192px.png',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\peegee_192px.png',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\peegee_256px.png',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\peegee_256px.png',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\<EMAIL>',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\<EMAIL>',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\peegee_512px.png',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\peegee_512px.png',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\<EMAIL>',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\<EMAIL>',
   'DATA'),
  ('pyqtgraph\\imageview\\ImageView.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\imageview\\ImageView.py',
   'DATA'),
  ('pyqtgraph\\imageview\\ImageViewTemplate_generic.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\imageview\\ImageViewTemplate_generic.py',
   'DATA'),
  ('pyqtgraph\\imageview\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\imageview\\__init__.py',
   'DATA'),
  ('pyqtgraph\\jupyter\\GraphicsView.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\jupyter\\GraphicsView.py',
   'DATA'),
  ('pyqtgraph\\jupyter\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\jupyter\\__init__.py',
   'DATA'),
  ('pyqtgraph\\metaarray\\MetaArray.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\metaarray\\MetaArray.py',
   'DATA'),
  ('pyqtgraph\\metaarray\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\metaarray\\__init__.py',
   'DATA'),
  ('pyqtgraph\\multiprocess\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\multiprocess\\__init__.py',
   'DATA'),
  ('pyqtgraph\\multiprocess\\bootstrap.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\multiprocess\\bootstrap.py',
   'DATA'),
  ('pyqtgraph\\multiprocess\\parallelizer.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\multiprocess\\parallelizer.py',
   'DATA'),
  ('pyqtgraph\\multiprocess\\processes.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\multiprocess\\processes.py',
   'DATA'),
  ('pyqtgraph\\multiprocess\\remoteproxy.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\multiprocess\\remoteproxy.py',
   'DATA'),
  ('pyqtgraph\\opengl\\GLGraphicsItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\GLGraphicsItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\GLViewWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\GLViewWidget.py',
   'DATA'),
  ('pyqtgraph\\opengl\\MeshData.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\MeshData.py',
   'DATA'),
  ('pyqtgraph\\opengl\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\__init__.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLAxisItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLAxisItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLBarGraphItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLBarGraphItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLBoxItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLBoxItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLGradientLegendItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLGradientLegendItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLGraphItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLGraphItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLGridItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLGridItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLImageItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLImageItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLLinePlotItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLLinePlotItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLMeshItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLMeshItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLScatterPlotItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLScatterPlotItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLSurfacePlotItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLSurfacePlotItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLTextItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLTextItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLVolumeItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLVolumeItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\__init__.py',
   'DATA'),
  ('pyqtgraph\\opengl\\shaders.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\shaders.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\Parameter.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\Parameter.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\ParameterItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\ParameterItem.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\ParameterSystem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\ParameterSystem.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\ParameterTree.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\ParameterTree.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\SystemSolver.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\SystemSolver.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\__init__.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\interactive.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\interactive.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\__init__.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\action.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\action.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\actiongroup.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\actiongroup.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\basetypes.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\basetypes.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\bool.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\bool.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\calendar.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\calendar.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\checklist.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\checklist.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\color.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\color.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\colormap.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\colormap.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\colormaplut.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\colormaplut.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\file.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\file.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\font.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\font.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\list.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\list.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\numeric.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\numeric.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\pen.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\pen.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\progress.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\progress.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\qtenum.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\qtenum.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\slider.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\slider.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\str.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\str.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\text.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\text.py',
   'DATA'),
  ('pyqtgraph\\reload.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\reload.py',
   'DATA'),
  ('pyqtgraph\\units.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\units.py',
   'DATA'),
  ('pyqtgraph\\util\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\__init__.py',
   'DATA'),
  ('pyqtgraph\\util\\colorama\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\colorama\\__init__.py',
   'DATA'),
  ('pyqtgraph\\util\\colorama\\win32.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\colorama\\win32.py',
   'DATA'),
  ('pyqtgraph\\util\\colorama\\winterm.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\colorama\\winterm.py',
   'DATA'),
  ('pyqtgraph\\util\\cprint.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\cprint.py',
   'DATA'),
  ('pyqtgraph\\util\\cupy_helper.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\cupy_helper.py',
   'DATA'),
  ('pyqtgraph\\util\\garbage_collector.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\garbage_collector.py',
   'DATA'),
  ('pyqtgraph\\util\\get_resolution.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\get_resolution.py',
   'DATA'),
  ('pyqtgraph\\util\\glinfo.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\glinfo.py',
   'DATA'),
  ('pyqtgraph\\util\\mutex.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\mutex.py',
   'DATA'),
  ('pyqtgraph\\util\\numba_helper.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\numba_helper.py',
   'DATA'),
  ('pyqtgraph\\widgets\\BusyCursor.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\BusyCursor.py',
   'DATA'),
  ('pyqtgraph\\widgets\\CheckTable.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\CheckTable.py',
   'DATA'),
  ('pyqtgraph\\widgets\\ColorButton.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ColorButton.py',
   'DATA'),
  ('pyqtgraph\\widgets\\ColorMapButton.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ColorMapButton.py',
   'DATA'),
  ('pyqtgraph\\widgets\\ColorMapMenu.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ColorMapMenu.py',
   'DATA'),
  ('pyqtgraph\\widgets\\ColorMapWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ColorMapWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\ComboBox.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ComboBox.py',
   'DATA'),
  ('pyqtgraph\\widgets\\DataFilterWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\DataFilterWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\DataTreeWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\DataTreeWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\DiffTreeWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\DiffTreeWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\FeedbackButton.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\FeedbackButton.py',
   'DATA'),
  ('pyqtgraph\\widgets\\FileDialog.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\FileDialog.py',
   'DATA'),
  ('pyqtgraph\\widgets\\GradientWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\GradientWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\GraphicsLayoutWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\GraphicsLayoutWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\GraphicsView.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\GraphicsView.py',
   'DATA'),
  ('pyqtgraph\\widgets\\GroupBox.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\GroupBox.py',
   'DATA'),
  ('pyqtgraph\\widgets\\HistogramLUTWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\HistogramLUTWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\JoystickButton.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\JoystickButton.py',
   'DATA'),
  ('pyqtgraph\\widgets\\LayoutWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\LayoutWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\MatplotlibWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\MatplotlibWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\MultiPlotWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\MultiPlotWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\PathButton.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\PathButton.py',
   'DATA'),
  ('pyqtgraph\\widgets\\PenPreviewLabel.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\PenPreviewLabel.py',
   'DATA'),
  ('pyqtgraph\\widgets\\PlotWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\PlotWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\ProgressDialog.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ProgressDialog.py',
   'DATA'),
  ('pyqtgraph\\widgets\\RawImageWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\RawImageWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\RemoteGraphicsView.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\RemoteGraphicsView.py',
   'DATA'),
  ('pyqtgraph\\widgets\\ScatterPlotWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ScatterPlotWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\SpinBox.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\SpinBox.py',
   'DATA'),
  ('pyqtgraph\\widgets\\TableWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\TableWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\TreeWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\TreeWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\ValueLabel.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ValueLabel.py',
   'DATA'),
  ('pyqtgraph\\widgets\\VerticalLabel.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\VerticalLabel.py',
   'DATA'),
  ('pyqtgraph\\widgets\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\__init__.py',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('PyQt6\\uic\\widget-plugins\\qtwebenginewidgets.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\widget-plugins\\qtwebenginewidgets.py',
   'DATA'),
  ('PyQt6\\uic\\widget-plugins\\qtprintsupport.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\widget-plugins\\qtprintsupport.py',
   'DATA'),
  ('PyQt6\\uic\\widget-plugins\\qtcharts.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\widget-plugins\\qtcharts.py',
   'DATA'),
  ('PyQt6\\uic\\widget-plugins\\qaxcontainer.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\widget-plugins\\qaxcontainer.py',
   'DATA'),
  ('PyQt6\\uic\\widget-plugins\\qtopenglwidgets.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\widget-plugins\\qtopenglwidgets.py',
   'DATA'),
  ('PyQt6\\uic\\widget-plugins\\qtquickwidgets.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\widget-plugins\\qtquickwidgets.py',
   'DATA'),
  ('PyQt6\\uic\\widget-plugins\\qscintilla.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\widget-plugins\\qscintilla.py',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ar.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ko.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ka.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fa.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ka.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fi.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_it.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fr.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_uk.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ka.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nl.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_he.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sk.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nn.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lt.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_bg.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_da.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sl.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pl.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ca.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_cs.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ja.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hr.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sv.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_tr.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_es.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lv.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_en.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_de.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gl.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hu.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ru.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gd.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gd.qm',
   'DATA'),
  ('numpy-2.3.1.dist-info\\METADATA',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy-2.3.1.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.3.1.dist-info\\INSTALLER',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy-2.3.1.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.3.1.dist-info\\entry_points.txt',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy-2.3.1.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.3.1.dist-info\\WHEEL',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy-2.3.1.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.3.1.dist-info\\RECORD',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy-2.3.1.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.3.1.dist-info\\DELVEWHEEL',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy-2.3.1.dist-info\\DELVEWHEEL',
   'DATA'),
  ('numpy-2.3.1.dist-info\\LICENSE.txt',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy-2.3.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('base_library.zip',
   'O:\\python\\shangweiji_test\\build\\TimeAndControl\\base_library.zip',
   'DATA')],
 'python312.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
