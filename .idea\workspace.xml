<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="6011419b-c0c5-4636-b7f7-9a46edb7be74" name="更改" comment="第13版，增加了调试窗口" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2zJixzh5QmAxlyoxvbvqPSAcBdy" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Python.main.executor&quot;: &quot;Run&quot;,
    &quot;Python.test1.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;last_opened_file_path&quot;: &quot;O:/python/shangweiji_test&quot;
  }
}</component>
  <component name="RunManager" selected="Python.main">
    <configuration name="main" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="shangweiji_test" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/main.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="test1" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="shangweiji_test" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/test1.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.main" />
        <item itemvalue="Python.test1" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-python-sdk-d68999036c7f-d3b881c8e49f-com.jetbrains.pycharm.community.sharedIndexes.bundled-PC-233.14475.56" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="6011419b-c0c5-4636-b7f7-9a46edb7be74" name="更改" comment="" />
      <created>1751455826958</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751455826958</updated>
    </task>
    <task id="LOCAL-00001" summary="第一版">
      <option name="closed" value="true" />
      <created>1751456122272</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1751456122272</updated>
    </task>
    <task id="LOCAL-00002" summary="第二版">
      <option name="closed" value="true" />
      <created>1751456828102</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1751456828102</updated>
    </task>
    <task id="LOCAL-00003" summary="第3版">
      <option name="closed" value="true" />
      <created>1751458085254</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1751458085254</updated>
    </task>
    <task id="LOCAL-00004" summary="第4版">
      <option name="closed" value="true" />
      <created>1751470500831</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1751470500831</updated>
    </task>
    <task id="LOCAL-00005" summary="第5版">
      <option name="closed" value="true" />
      <created>1751472542393</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1751472542393</updated>
    </task>
    <task id="LOCAL-00006" summary="第6版">
      <option name="closed" value="true" />
      <created>1751473087789</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1751473087789</updated>
    </task>
    <task id="LOCAL-00007" summary="第7版">
      <option name="closed" value="true" />
      <created>1751476201421</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1751476201421</updated>
    </task>
    <task id="LOCAL-00008" summary="第8版">
      <option name="closed" value="true" />
      <created>1751477036054</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1751477036054</updated>
    </task>
    <task id="LOCAL-00009" summary="第9版">
      <option name="closed" value="true" />
      <created>1751507405705</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1751507405705</updated>
    </task>
    <task id="LOCAL-00010" summary="第10版">
      <option name="closed" value="true" />
      <created>1751510098797</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1751510098797</updated>
    </task>
    <task id="LOCAL-00011" summary="第11版">
      <option name="closed" value="true" />
      <created>1751555186253</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1751555186253</updated>
    </task>
    <task id="LOCAL-00012" summary="第12版，修复曲线问题">
      <option name="closed" value="true" />
      <created>1751597278583</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1751597278583</updated>
    </task>
    <task id="LOCAL-00013" summary="第13版，增加了调试窗口">
      <option name="closed" value="true" />
      <created>1751603545142</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1751603545142</updated>
    </task>
    <option name="localTasksCounter" value="14" />
    <servers />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="第一版" />
    <MESSAGE value="第二版" />
    <MESSAGE value="第3版" />
    <MESSAGE value="第4版" />
    <MESSAGE value="第5版" />
    <MESSAGE value="第6版" />
    <MESSAGE value="第7版" />
    <MESSAGE value="第8版" />
    <MESSAGE value="第9版" />
    <MESSAGE value="第10版" />
    <MESSAGE value="第11版" />
    <MESSAGE value="第12版，修复曲线问题" />
    <MESSAGE value="第13版，增加了调试窗口" />
    <option name="LAST_COMMIT_MESSAGE" value="第13版，增加了调试窗口" />
  </component>
</project>