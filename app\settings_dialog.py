import sys
from PyQt6.QtWidgets import (QA<PERSON><PERSON>, QDialog, QWidget, QVBoxLayout, QHBoxLayout, 
                             QPushButton, QLabel, QLineEdit, QGridLayout, QSpacerItem, QSizePolicy, QFrame, QComboBox, QDialogButtonBox)
from PyQt6.QtCore import Qt, QPoint, pyqtSignal
from PyQt6.QtGui import QFont
from .styles import MAIN_STYLESHEET

class SettingsDialog(QDialog):
    settings_applied = pyqtSignal(dict)
    
    def __init__(self, parent=None, current_settings=None):
        super().__init__(parent)
        self.setModal(True)
        self.setWindowFlag(Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        self.setObjectName("SettingsDialog")
        
        # Store current settings
        if current_settings is None:
            current_settings = {'max_plot_time': 1000, 'min_temp': 400, 'max_temp': 3600}
        else:
            # 确保所有必需的键都存在，但不覆盖传递进来的值
            if 'response_time' not in current_settings:
                current_settings['response_time'] = 100
        
        self.current_settings = current_settings
        
        # For dragging the window
        self._drag_pos = QPoint()

        # Main frame
        self.background_frame = QFrame(self)
        self.background_frame.setObjectName("SettingsBackgroundFrame")
        
        # --- Main Layout ---
        main_layout = QVBoxLayout(self.background_frame)
        main_layout.setContentsMargins(1, 1, 1, 1) # Add a thin margin for the border
        main_layout.setSpacing(0)

        # 1. Custom Title Bar
        title_bar_layout = QHBoxLayout()
        title_bar_layout.setContentsMargins(15, 0, 5, 0)
        title_label = QLabel("Settings")
        title_label.setObjectName("SettingsTitle")
        
        close_button = QPushButton("✕")
        close_button.setObjectName("SettingsCloseButton")
        close_button.clicked.connect(self.reject) # Reject closes without saving
        
        title_bar_layout.addWidget(title_label)
        title_bar_layout.addStretch()
        title_bar_layout.addWidget(close_button)
        
        main_layout.addLayout(title_bar_layout)
        main_layout.addSpacing(20)

        # 2. Controls Layout - Use QGridLayout for precise alignment
        controls_layout = QGridLayout()
        controls_layout.setContentsMargins(40, 10, 40, 10)
        controls_layout.setHorizontalSpacing(10)
        controls_layout.setVerticalSpacing(15)

        # Row 0: Max Plot Time
        plot_time_label = QLabel("Max Plot time:")
        self.max_plot_time_input = QLineEdit()
        self.max_plot_time_input.setObjectName("SettingsInput")
        self.max_plot_time_input.setText(str(current_settings.get('max_plot_duration', 100.0)))
        unit_s_label = QLabel("s")

        controls_layout.addWidget(plot_time_label, 0, 0, Qt.AlignmentFlag.AlignRight)
        controls_layout.addWidget(self.max_plot_time_input, 0, 1, 1, 2) # Span 1 row, 2 columns
        controls_layout.addWidget(unit_s_label, 0, 3, Qt.AlignmentFlag.AlignLeft)

        # Row 1: T min & max
        temp_range_label = QLabel("T min & max:")
        self.t_min_input = QLineEdit()
        self.t_min_input.setObjectName("SettingsInput")
        self.t_min_input.setText(str(current_settings.get('min_temp', 400)))
        
        self.t_max_input = QLineEdit()
        self.t_max_input.setObjectName("SettingsInput")
        self.t_max_input.setText(str(current_settings.get('max_temp', 3600)))

        unit_c_label = QLabel("°C")

        controls_layout.addWidget(temp_range_label, 1, 0, Qt.AlignmentFlag.AlignRight)
        controls_layout.addWidget(self.t_min_input, 1, 1)
        controls_layout.addWidget(self.t_max_input, 1, 2)
        controls_layout.addWidget(unit_c_label, 1, 3, Qt.AlignmentFlag.AlignLeft)

        # Set column stretch to make inputs expand equally
        controls_layout.setColumnStretch(1, 1)
        controls_layout.setColumnStretch(2, 1)
        
        main_layout.addLayout(controls_layout)
        main_layout.addStretch(1)

        # 3. OK Button
        button_layout = QHBoxLayout()
        self.ok_button = QPushButton("OK")
        self.ok_button.setObjectName("SettingsOKButton")
        self.ok_button.clicked.connect(self.apply_and_close)
        button_layout.addStretch()
        button_layout.addWidget(self.ok_button)
        button_layout.addStretch()
        
        main_layout.addLayout(button_layout)
        main_layout.addSpacing(20)

        self.setLayout(QVBoxLayout(self))
        self.layout().addWidget(self.background_frame)
        self.layout().setContentsMargins(0,0,0,0)

    def apply_and_close(self):
        """验证、发射信号并关闭对话框"""
        try:
            settings = {
                'max_plot_duration': float(self.max_plot_time_input.text()),
                'min_temp': int(self.t_min_input.text()),
                'max_temp': int(self.t_max_input.text()),
            }
            self.settings_applied.emit(settings)
            self.accept()
        except (ValueError, TypeError):
            # 在这里可以添加一个QMessageBox来提示用户输入无效
            print("Error: Invalid input values in settings dialog.")
            pass # Or show an error message

    def mousePressEvent(self, event):
        """Capture the mouse press position for dragging."""
        if event.button() == Qt.MouseButton.LeftButton:
            self._drag_pos = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        """Move the window if the mouse is being dragged."""
        if event.buttons() == Qt.MouseButton.LeftButton:
            self.move(event.globalPosition().toPoint() - self._drag_pos)
            event.accept()

if __name__ == '__main__':
    app = QApplication(sys.argv)
    
    # Example of how to pass styles
    app.setStyleSheet(MAIN_STYLESHEET)

    dialog = SettingsDialog()
    if dialog.exec():
        print(dialog.get_values())
    sys.exit()
