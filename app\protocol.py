# This file is for creating protocol messages
import struct

def create_simple_command(cmd: str) -> bytes:
    """Creates a command that is just a simple string."""
    return cmd.encode('ascii')

def create_value_command(cmd_char: str, value: int) -> bytes:
    """Creates a command with a 16-bit unsigned integer value (big-endian)."""
    # Using >H for big-endian unsigned short (16-bit)
    return f"{cmd_char} ".encode('ascii') + struct.pack('>H', value)

# --- Main Controls ---
def get_start_plotting_msg():
    return create_simple_command("A 1")

def get_stop_plotting_msg():
    return create_simple_command("A 0")

def get_measure_resistance_on_msg():
    return create_simple_command("B 1")

def get_measure_resistance_off_msg():
    return create_simple_command("B 0")

def get_set_duty_ratio_msg(duty_ratio: int):
    """Duty Ratio as 16-bit unsigned int"""
    return create_value_command("C", duty_ratio)

def get_set_flash_time_msg(flash_time: int):
    """Flash Time as 16-bit unsigned int"""
    return create_value_command("D", flash_time)

def get_heat_on_msg():
    return create_simple_command("E 1")

def get_heat_off_msg():
    return create_simple_command("E 0")

def get_request_plot_data_msg():
    """Request plot data - sends F followed by 0x01"""
    return b'F\x01'

# --- Settings ---
def get_set_max_plot_time_msg(time_val: int):
    return create_value_command("H", time_val)

def get_set_t_min_msg(temp: int):
    return create_value_command("I", temp)

def get_set_t_max_msg(temp: int):
    return create_value_command("J", temp)

# --- Status ---
def get_request_status_msg():
    return create_simple_command("SSS")

if __name__ == '__main__':
    print(f"'Start Plotting' command: {get_start_plotting_msg()}")
    # E.g., for Duty Ratio of 99.9, we might send 999. The hardware would divide by 10.
    # Let's assume we send the value directly as an integer.
    duty_value = 999
    print(f"'Set Duty Ratio' to {duty_value}: {get_set_duty_ratio_msg(duty_value).hex(' ')}")
    # Expected: 'C' (43) + space (20) + 999 (0x03E7) in big-endian (03 e7)
    # -> 43 20 03 e7
    assert get_set_duty_ratio_msg(999) == b'C \x03\xe7'
    
    # Test the new plot data request
    print(f"'Request Plot Data' command: {get_request_plot_data_msg().hex(' ')}")
    assert get_request_plot_data_msg() == b'F\x01'
    print("All tests passed.") 