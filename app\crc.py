import crc16

def calculate_crc16_modbus(data: bytes) -> bytes:
    """
    Calculates the CRC-16/MODBUS for the given data.
    
    Args:
        data: A bytes object containing the data to be checksummed.
        
    Returns:
        A 2-byte bytes object representing the CRC in little-endian format.
    """
    crc_value = crc16.crc16modbus(data)
    # The protocol seems to use little-endian for the CRC
    return crc_value.to_bytes(2, 'little')

# Example from documentation: "X" + data count (1 byte) + 20 * (16-bit unsigned int temperature data) + CRC16
# where CRC16 is 5C 16. This suggests the CRC is calculated on the payload.
# We will need the actual payload to verify this function. Let's assume this is correct for now.

if __name__ == '__main__':
    # This is a test case. Replace with actual data payload from protocol doc if available.
    # Let's assume a sample payload.
    # The example given "45 01/58 28 ... 5C 16" is confusing.
    # Let's try to generate a CRC for a hypothetical payload.
    payload = b'\x01\x02\x03\x04\x05'
    crc = calculate_crc16_modbus(payload)
    print(f"Payload: {payload.hex()}")
    print(f"CRC-16/MODBUS (little-endian): {crc.hex()}")

    # Another test
    payload2 = b'123456789'
    crc2 = crc16.crc16modbus(payload2)
    # Expected for '123456789' with CRC-16/MODBUS is 0x4B37.
    # In little-endian bytes: 37 4B
    print(f"Payload: {payload2.decode()}")
    print(f"CRC-16/MODBUS (little-endian): {calculate_crc16_modbus(payload2).hex()}")
    assert calculate_crc16_modbus(payload2) == b'\x37\x4b'
    print("Test passed.")
