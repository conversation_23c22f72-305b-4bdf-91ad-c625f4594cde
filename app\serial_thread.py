import time
import serial
import serial.tools.list_ports
from PyQt6.QtCore import QThread, pyqtSignal, QMutex, QMutexLocker
import struct
from .crc import calculate_crc16_modbus
from . import protocol

class SerialThread(QThread):
    # Signals
    data_received = pyqtSignal(list) # list of 20 temperatures
    status_updated = pyqtSignal(dict)
    resistance_received = pyqtSignal(int)
    port_list_updated = pyqtSignal(list)
    connection_status = pyqtSignal(bool)
    error_message = pyqtSignal(str)
    command_ack_received = pyqtSignal(bytes) # Signal for command acknowledgements
    log_message = pyqtSignal(str) # For logging raw communication

    def __init__(self, parent=None):
        super().__init__(parent)
        self.serial_port = serial.Serial()
        self.is_running = False
        self.buffer = bytearray()
        # self._find_ports() # Removed from here

    @property
    def is_connected(self):
        return self.serial_port.is_open

    def scan_for_ports(self):
        """Scans for available serial ports and emits the list."""
        self._find_ports()

    def _find_ports(self):
        ports = [port.device for port in serial.tools.list_ports.comports()]
        self.port_list_updated.emit(ports)

    def connect_port(self, port_name, baud_rate=115200):
        try:
            if self.serial_port.is_open:
                self.serial_port.close()
            
            self.serial_port.port = port_name
            self.serial_port.baudrate = baud_rate
            self.serial_port.timeout = 0.1 # Read timeout
            self.serial_port.open()
            
            self.is_running = True
            self.connection_status.emit(True)
            self.start() # Start the thread's run method
            return True
        except serial.SerialException as e:
            self.error_message.emit(f"Error opening serial port: {e}")
            self.connection_status.emit(False)
            return False

    def disconnect_port(self):
        self.is_running = False
        if self.serial_port.is_open:
            self.serial_port.close()
        self.connection_status.emit(False)
        self.wait() # Wait for thread to finish

    def send_data(self, data: bytes):
        if self.serial_port.is_open:
            try:
                self.serial_port.write(data)
                self.log_message.emit(f"--> SENT: {data.hex(' ')}")
            except Exception as e:
                self.error_message.emit(f"Error writing to serial port: {e}")

    def run(self):
        """Main loop to read and parse data from the serial port."""
        while self.is_running:
            if self.serial_port and self.serial_port.is_open:
                try:
                    bytes_to_read = self.serial_port.in_waiting
                    if bytes_to_read > 0:
                        data = self.serial_port.read(bytes_to_read)
                        self.log_message.emit(f"<-- RECV: {data.hex(' ')}")
                        self.buffer.extend(data)

                    # Process buffer
                    while len(self.buffer) >= 3: # Minimum packet size (e.g., ACK/NACK)
                        if self.buffer.startswith(b'F'):  # Curve data packet
                            if len(self.buffer) >= 2: # Check for marker and length byte
                                num_data_bytes = self.buffer[1]
                                packet_len = 1 + 1 + num_data_bytes + 2  # F, len, data, crc16
                                if len(self.buffer) >= packet_len:
                                    packet = self.buffer[:packet_len]
                                    
                                    # CRC Check: Compare received CRC with calculated CRC
                                    received_crc = packet[-2:]
                                    calculated_crc = calculate_crc16_modbus(packet[:-2])
                                    
                                    if received_crc != calculated_crc:
                                        self.error_message.emit(f"CRC mismatch. Got: {received_crc.hex()}, Expected: {calculated_crc.hex()}. Packet discarded.")
                                        self.buffer = self.buffer[packet_len:]  # Discard the invalid packet
                                        continue  # Move to the next part of the buffer

                                    data_bytes = packet[2:-2]
                                    if len(data_bytes) % 2 == 0:
                                        values = [struct.unpack('>H', data_bytes[i:i+2])[0] for i in range(0, len(data_bytes), 2)]
                                        self.data_received.emit(values)
                                    else:
                                        self.error_message.emit("Odd number of data bytes in F packet.")
                                    
                                    self.buffer = self.buffer[packet_len:]
                                else:
                                    break # Not enough data for a full F packet
                            else:
                                break # Not enough data for F packet header

                        elif self.buffer.startswith(b'B'):
                            if len(self.buffer) >= 3:
                                # This assumes B packet is always resistance, which might be wrong.
                                # Let's assume 'B' followed by 2 bytes is resistance for now.
                                # A better protocol would have more structure.
                                val = struct.unpack('>H', self.buffer[1:3])[0]
                                self.resistance_received.emit(val)
                                self.buffer = self.buffer[3:]
                            else:
                                break # Not enough data for B packet

                        elif self.buffer[:3] in [b'AOK', b'BOK', b'COK', b'DOK', b'EOK', b'HOK', b'IOK', b'JOK']:
                            ack_message = self.buffer[:3]
                            self.command_ack_received.emit(ack_message)
                            self.buffer = self.buffer[3:]
                        
                        else:
                            # To prevent getting stuck on malformed data, discard one byte
                            self.error_message.emit(f"Buffer desync, discarding one byte: {self.buffer[0]:02X}")
                            self.buffer = self.buffer[1:]
                
                except serial.SerialException as e:
                    self.error_message.emit(f"Serial error: {e}")
                    self.disconnect_port()
            else:
                time.sleep(0.1) # Wait if not connected
        
        # Cleanup when thread stops
        if self.serial_port and self.serial_port.is_open:
            self.serial_port.close()
            self.connection_status.emit(False)
