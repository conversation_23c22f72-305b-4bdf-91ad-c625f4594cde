('O:\\python\\shangweiji_test\\build\\TimeAndControl\\PYZ-00.pyz',
 [('PyQt6',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\__init__.py',
   'PYMODULE'),
  ('PyQt6.uic',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\__init__.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\__init__.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.as_string',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\as_string.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.compiler',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\compiler.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.indenter',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\indenter.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.misc',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\misc.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.proxy_metaclass',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\proxy_metaclass.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.qobjectcreator',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\qobjectcreator.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.qtproxies',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\qtproxies.py',
   'PYMODULE'),
  ('PyQt6.uic.Loader',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Loader\\__init__.py',
   'PYMODULE'),
  ('PyQt6.uic.Loader.loader',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Loader\\loader.py',
   'PYMODULE'),
  ('PyQt6.uic.Loader.qobjectcreator',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Loader\\qobjectcreator.py',
   'PYMODULE'),
  ('PyQt6.uic.compile_ui',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\compile_ui.py',
   'PYMODULE'),
  ('PyQt6.uic.enum_map',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\enum_map.py',
   'PYMODULE'),
  ('PyQt6.uic.exceptions',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\exceptions.py',
   'PYMODULE'),
  ('PyQt6.uic.icon_cache',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\icon_cache.py',
   'PYMODULE'),
  ('PyQt6.uic.load_ui',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\load_ui.py',
   'PYMODULE'),
  ('PyQt6.uic.objcreator',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\objcreator.py',
   'PYMODULE'),
  ('PyQt6.uic.properties',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\properties.py',
   'PYMODULE'),
  ('PyQt6.uic.ui_file',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\ui_file.py',
   'PYMODULE'),
  ('PyQt6.uic.uiparser',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\uiparser.py',
   'PYMODULE'),
  ('__future__',
   'I:\\Program Files\\python\\python12\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'I:\\Program Files\\python\\python12\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_compat_pickle',
   'I:\\Program Files\\python\\python12\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'I:\\Program Files\\python\\python12\\Lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_py_abc',
   'I:\\Program Files\\python\\python12\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydatetime',
   'I:\\Program Files\\python\\python12\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   'I:\\Program Files\\python\\python12\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'I:\\Program Files\\python\\python12\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime',
   'I:\\Program Files\\python\\python12\\Lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'I:\\Program Files\\python\\python12\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('app', 'O:\\python\\shangweiji_test\\app\\__init__.py', 'PYMODULE'),
  ('app.crc', 'O:\\python\\shangweiji_test\\app\\crc.py', 'PYMODULE'),
  ('app.main_window',
   'O:\\python\\shangweiji_test\\app\\main_window.py',
   'PYMODULE'),
  ('app.mock_serial_thread',
   'O:\\python\\shangweiji_test\\app\\mock_serial_thread.py',
   'PYMODULE'),
  ('app.protocol', 'O:\\python\\shangweiji_test\\app\\protocol.py', 'PYMODULE'),
  ('app.serial_thread',
   'O:\\python\\shangweiji_test\\app\\serial_thread.py',
   'PYMODULE'),
  ('app.settings_dialog',
   'O:\\python\\shangweiji_test\\app\\settings_dialog.py',
   'PYMODULE'),
  ('app.styles', 'O:\\python\\shangweiji_test\\app\\styles.py', 'PYMODULE'),
  ('argparse',
   'I:\\Program Files\\python\\python12\\Lib\\argparse.py',
   'PYMODULE'),
  ('ast', 'I:\\Program Files\\python\\python12\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('backports',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'I:\\Program Files\\python\\python12\\Lib\\base64.py', 'PYMODULE'),
  ('bdb', 'I:\\Program Files\\python\\python12\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'I:\\Program Files\\python\\python12\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'I:\\Program Files\\python\\python12\\Lib\\bz2.py', 'PYMODULE'),
  ('cProfile',
   'I:\\Program Files\\python\\python12\\Lib\\cProfile.py',
   'PYMODULE'),
  ('calendar',
   'I:\\Program Files\\python\\python12\\Lib\\calendar.py',
   'PYMODULE'),
  ('cmd', 'I:\\Program Files\\python\\python12\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'I:\\Program Files\\python\\python12\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'I:\\Program Files\\python\\python12\\Lib\\codeop.py', 'PYMODULE'),
  ('concurrent',
   'I:\\Program Files\\python\\python12\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'I:\\Program Files\\python\\python12\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'I:\\Program Files\\python\\python12\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'I:\\Program Files\\python\\python12\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'I:\\Program Files\\python\\python12\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'I:\\Program Files\\python\\python12\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'I:\\Program Files\\python\\python12\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'I:\\Program Files\\python\\python12\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'I:\\Program Files\\python\\python12\\Lib\\copy.py', 'PYMODULE'),
  ('crc16',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\crc16\\__init__.py',
   'PYMODULE'),
  ('crc16.crc16pure',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\crc16\\crc16pure.py',
   'PYMODULE'),
  ('csv', 'I:\\Program Files\\python\\python12\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'I:\\Program Files\\python\\python12\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'I:\\Program Files\\python\\python12\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'I:\\Program Files\\python\\python12\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'I:\\Program Files\\python\\python12\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'I:\\Program Files\\python\\python12\\Lib\\datetime.py',
   'PYMODULE'),
  ('decimal',
   'I:\\Program Files\\python\\python12\\Lib\\decimal.py',
   'PYMODULE'),
  ('difflib',
   'I:\\Program Files\\python\\python12\\Lib\\difflib.py',
   'PYMODULE'),
  ('dis', 'I:\\Program Files\\python\\python12\\Lib\\dis.py', 'PYMODULE'),
  ('doctest',
   'I:\\Program Files\\python\\python12\\Lib\\doctest.py',
   'PYMODULE'),
  ('email',
   'I:\\Program Files\\python\\python12\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'I:\\Program Files\\python\\python12\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'I:\\Program Files\\python\\python12\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'I:\\Program Files\\python\\python12\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'I:\\Program Files\\python\\python12\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'I:\\Program Files\\python\\python12\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'I:\\Program Files\\python\\python12\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'I:\\Program Files\\python\\python12\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'I:\\Program Files\\python\\python12\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'I:\\Program Files\\python\\python12\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'I:\\Program Files\\python\\python12\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'I:\\Program Files\\python\\python12\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'I:\\Program Files\\python\\python12\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'I:\\Program Files\\python\\python12\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'I:\\Program Files\\python\\python12\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'I:\\Program Files\\python\\python12\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'I:\\Program Files\\python\\python12\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'I:\\Program Files\\python\\python12\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'I:\\Program Files\\python\\python12\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'I:\\Program Files\\python\\python12\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('fileinput',
   'I:\\Program Files\\python\\python12\\Lib\\fileinput.py',
   'PYMODULE'),
  ('fnmatch',
   'I:\\Program Files\\python\\python12\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'I:\\Program Files\\python\\python12\\Lib\\fractions.py',
   'PYMODULE'),
  ('ftplib', 'I:\\Program Files\\python\\python12\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'I:\\Program Files\\python\\python12\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass',
   'I:\\Program Files\\python\\python12\\Lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'I:\\Program Files\\python\\python12\\Lib\\gettext.py',
   'PYMODULE'),
  ('glob', 'I:\\Program Files\\python\\python12\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'I:\\Program Files\\python\\python12\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib',
   'I:\\Program Files\\python\\python12\\Lib\\hashlib.py',
   'PYMODULE'),
  ('hmac', 'I:\\Program Files\\python\\python12\\Lib\\hmac.py', 'PYMODULE'),
  ('html',
   'I:\\Program Files\\python\\python12\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'I:\\Program Files\\python\\python12\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('http',
   'I:\\Program Files\\python\\python12\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'I:\\Program Files\\python\\python12\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'I:\\Program Files\\python\\python12\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server',
   'I:\\Program Files\\python\\python12\\Lib\\http\\server.py',
   'PYMODULE'),
  ('importlib',
   'I:\\Program Files\\python\\python12\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'I:\\Program Files\\python\\python12\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'I:\\Program Files\\python\\python12\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'I:\\Program Files\\python\\python12\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'I:\\Program Files\\python\\python12\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'I:\\Program Files\\python\\python12\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'I:\\Program Files\\python\\python12\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'I:\\Program Files\\python\\python12\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'I:\\Program Files\\python\\python12\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'I:\\Program Files\\python\\python12\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'I:\\Program Files\\python\\python12\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'I:\\Program Files\\python\\python12\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'I:\\Program Files\\python\\python12\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'I:\\Program Files\\python\\python12\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'I:\\Program Files\\python\\python12\\Lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'I:\\Program Files\\python\\python12\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('json',
   'I:\\Program Files\\python\\python12\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'I:\\Program Files\\python\\python12\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'I:\\Program Files\\python\\python12\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'I:\\Program Files\\python\\python12\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'I:\\Program Files\\python\\python12\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'I:\\Program Files\\python\\python12\\Lib\\lzma.py', 'PYMODULE'),
  ('mimetypes',
   'I:\\Program Files\\python\\python12\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   'I:\\Program Files\\python\\python12\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'I:\\Program Files\\python\\python12\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'I:\\Program Files\\python\\python12\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'I:\\Program Files\\python\\python12\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'I:\\Program Files\\python\\python12\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'I:\\Program Files\\python\\python12\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'I:\\Program Files\\python\\python12\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'I:\\Program Files\\python\\python12\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'I:\\Program Files\\python\\python12\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'I:\\Program Files\\python\\python12\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'I:\\Program Files\\python\\python12\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'I:\\Program Files\\python\\python12\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'I:\\Program Files\\python\\python12\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'I:\\Program Files\\python\\python12\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'I:\\Program Files\\python\\python12\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'I:\\Program Files\\python\\python12\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path',
   'I:\\Program Files\\python\\python12\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'I:\\Program Files\\python\\python12\\Lib\\numbers.py',
   'PYMODULE'),
  ('numpy',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\ctypeslib\\__init__.py',
   'PYMODULE'),
  ('numpy.ctypeslib._ctypeslib',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\ctypeslib\\_ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._format_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_format_impl.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'I:\\Program Files\\python\\python12\\Lib\\opcode.py', 'PYMODULE'),
  ('optparse',
   'I:\\Program Files\\python\\python12\\Lib\\optparse.py',
   'PYMODULE'),
  ('packaging',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib',
   'I:\\Program Files\\python\\python12\\Lib\\pathlib.py',
   'PYMODULE'),
  ('pdb', 'I:\\Program Files\\python\\python12\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'I:\\Program Files\\python\\python12\\Lib\\pickle.py', 'PYMODULE'),
  ('pkgutil',
   'I:\\Program Files\\python\\python12\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'I:\\Program Files\\python\\python12\\Lib\\platform.py',
   'PYMODULE'),
  ('pprint', 'I:\\Program Files\\python\\python12\\Lib\\pprint.py', 'PYMODULE'),
  ('profile',
   'I:\\Program Files\\python\\python12\\Lib\\profile.py',
   'PYMODULE'),
  ('pstats', 'I:\\Program Files\\python\\python12\\Lib\\pstats.py', 'PYMODULE'),
  ('py_compile',
   'I:\\Program Files\\python\\python12\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pydoc', 'I:\\Program Files\\python\\python12\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'I:\\Program Files\\python\\python12\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'I:\\Program Files\\python\\python12\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pyqtgraph',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.GraphicsScene',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.GraphicsScene.GraphicsScene',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\GraphicsScene.py',
   'PYMODULE'),
  ('pyqtgraph.GraphicsScene.exportDialog',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\exportDialog.py',
   'PYMODULE'),
  ('pyqtgraph.GraphicsScene.exportDialogTemplate_generic',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\exportDialogTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.GraphicsScene.mouseEvents',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\mouseEvents.py',
   'PYMODULE'),
  ('pyqtgraph.PlotData',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\PlotData.py',
   'PYMODULE'),
  ('pyqtgraph.Point',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Point.py',
   'PYMODULE'),
  ('pyqtgraph.Qt',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.Qt.QtCore',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtCore\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.Qt.QtGui',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtGui\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.Qt.QtWidgets',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtWidgets\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.Qt.compat',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\compat\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.Qt.internals',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\internals.py',
   'PYMODULE'),
  ('pyqtgraph.SRTTransform',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\SRTTransform.py',
   'PYMODULE'),
  ('pyqtgraph.SRTTransform3D',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\SRTTransform3D.py',
   'PYMODULE'),
  ('pyqtgraph.SignalProxy',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\SignalProxy.py',
   'PYMODULE'),
  ('pyqtgraph.ThreadsafeTimer',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\ThreadsafeTimer.py',
   'PYMODULE'),
  ('pyqtgraph.Transform3D',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Transform3D.py',
   'PYMODULE'),
  ('pyqtgraph.Vector',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Vector.py',
   'PYMODULE'),
  ('pyqtgraph.WidgetGroup',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\WidgetGroup.py',
   'PYMODULE'),
  ('pyqtgraph.canvas',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.canvas.Canvas',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\Canvas.py',
   'PYMODULE'),
  ('pyqtgraph.canvas.CanvasItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\CanvasItem.py',
   'PYMODULE'),
  ('pyqtgraph.canvas.CanvasManager',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\CanvasManager.py',
   'PYMODULE'),
  ('pyqtgraph.canvas.CanvasTemplate_generic',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\CanvasTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.canvas.TransformGuiTemplate_generic',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\TransformGuiTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.colormap',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colormap.py',
   'PYMODULE'),
  ('pyqtgraph.colors',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.colors.palette',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\palette.py',
   'PYMODULE'),
  ('pyqtgraph.configfile',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\configfile.py',
   'PYMODULE'),
  ('pyqtgraph.console',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.console.CmdInput',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\CmdInput.py',
   'PYMODULE'),
  ('pyqtgraph.console.Console',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\Console.py',
   'PYMODULE'),
  ('pyqtgraph.console.exception_widget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\exception_widget.py',
   'PYMODULE'),
  ('pyqtgraph.console.repl_widget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\repl_widget.py',
   'PYMODULE'),
  ('pyqtgraph.console.stackwidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\stackwidget.py',
   'PYMODULE'),
  ('pyqtgraph.debug',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\debug.py',
   'PYMODULE'),
  ('pyqtgraph.dockarea',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\dockarea\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.dockarea.Container',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\dockarea\\Container.py',
   'PYMODULE'),
  ('pyqtgraph.dockarea.Dock',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\dockarea\\Dock.py',
   'PYMODULE'),
  ('pyqtgraph.dockarea.DockArea',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\dockarea\\DockArea.py',
   'PYMODULE'),
  ('pyqtgraph.dockarea.DockDrop',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\dockarea\\DockDrop.py',
   'PYMODULE'),
  ('pyqtgraph.examples',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.examples.Arrow',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\Arrow.py',
   'PYMODULE'),
  ('pyqtgraph.examples.AxisItem_label_overlap',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\AxisItem_label_overlap.py',
   'PYMODULE'),
  ('pyqtgraph.examples.BarGraphItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\BarGraphItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.CLIexample',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\CLIexample.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ColorBarItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ColorBarItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ColorButton',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ColorButton.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ColorGradientPlots',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ColorGradientPlots.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ConsoleWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ConsoleWidget.py',
   'PYMODULE'),
  ('pyqtgraph.examples.CustomGraphItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\CustomGraphItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.DataSlicing',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\DataSlicing.py',
   'PYMODULE'),
  ('pyqtgraph.examples.DataTreeWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\DataTreeWidget.py',
   'PYMODULE'),
  ('pyqtgraph.examples.DateAxisItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\DateAxisItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.DateAxisItem_QtDesigner',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\DateAxisItem_QtDesigner.py',
   'PYMODULE'),
  ('pyqtgraph.examples.DiffTreeWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\DiffTreeWidget.py',
   'PYMODULE'),
  ('pyqtgraph.examples.Draw',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\Draw.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ErrorBarItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ErrorBarItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ExampleApp',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ExampleApp.py',
   'PYMODULE'),
  ('pyqtgraph.examples.FillBetweenItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\FillBetweenItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.Flowchart',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\Flowchart.py',
   'PYMODULE'),
  ('pyqtgraph.examples.FlowchartCustomNode',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\FlowchartCustomNode.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GLBarGraphItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLBarGraphItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GLGradientLegendItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLGradientLegendItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GLGraphItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLGraphItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GLImageItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLImageItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GLIsosurface',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLIsosurface.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GLLinePlotItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLLinePlotItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GLMeshItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLMeshItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GLPainterItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLPainterItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GLScatterPlotItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLScatterPlotItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GLSurfacePlot',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLSurfacePlot.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GLTextItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLTextItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GLViewWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLViewWidget.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GLVolumeItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLVolumeItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GLshaders',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLshaders.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GradientEditor',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GradientEditor.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GradientWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GradientWidget.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GraphItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GraphItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GraphicsLayout',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GraphicsLayout.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GraphicsScene',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GraphicsScene.py',
   'PYMODULE'),
  ('pyqtgraph.examples.HistogramLUT',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\HistogramLUT.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ImageItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ImageItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ImageView',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ImageView.py',
   'PYMODULE'),
  ('pyqtgraph.examples.InfiniteLine',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\InfiniteLine.py',
   'PYMODULE'),
  ('pyqtgraph.examples.InteractiveParameter',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\InteractiveParameter.py',
   'PYMODULE'),
  ('pyqtgraph.examples.JoystickButton',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\JoystickButton.py',
   'PYMODULE'),
  ('pyqtgraph.examples.Legend',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\Legend.py',
   'PYMODULE'),
  ('pyqtgraph.examples.LogPlotTest',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\LogPlotTest.py',
   'PYMODULE'),
  ('pyqtgraph.examples.MatrixDisplayExample',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\MatrixDisplayExample.py',
   'PYMODULE'),
  ('pyqtgraph.examples.MouseSelection',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\MouseSelection.py',
   'PYMODULE'),
  ('pyqtgraph.examples.MultiDataPlot',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\MultiDataPlot.py',
   'PYMODULE'),
  ('pyqtgraph.examples.MultiPlotSpeedTest',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\MultiPlotSpeedTest.py',
   'PYMODULE'),
  ('pyqtgraph.examples.MultiplePlotAxes',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\MultiplePlotAxes.py',
   'PYMODULE'),
  ('pyqtgraph.examples.NonUniformImage',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\NonUniformImage.py',
   'PYMODULE'),
  ('pyqtgraph.examples.PColorMeshItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\PColorMeshItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.PanningPlot',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\PanningPlot.py',
   'PYMODULE'),
  ('pyqtgraph.examples.PlotAutoRange',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\PlotAutoRange.py',
   'PYMODULE'),
  ('pyqtgraph.examples.PlotSpeedTest',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\PlotSpeedTest.py',
   'PYMODULE'),
  ('pyqtgraph.examples.PlotWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\PlotWidget.py',
   'PYMODULE'),
  ('pyqtgraph.examples.Plotting',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\Plotting.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ProgressDialog',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ProgressDialog.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ROIExamples',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ROIExamples.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ROItypes',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ROItypes.py',
   'PYMODULE'),
  ('pyqtgraph.examples.RemoteGraphicsView',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\RemoteGraphicsView.py',
   'PYMODULE'),
  ('pyqtgraph.examples.RemoteSpeedTest',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\RemoteSpeedTest.py',
   'PYMODULE'),
  ('pyqtgraph.examples.RunExampleApp',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\RunExampleApp.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ScaleBar',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ScaleBar.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ScatterPlot',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ScatterPlot.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ScatterPlotSpeedTest',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ScatterPlotSpeedTest.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ScatterPlotWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ScatterPlotWidget.py',
   'PYMODULE'),
  ('pyqtgraph.examples.SimplePlot',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\SimplePlot.py',
   'PYMODULE'),
  ('pyqtgraph.examples.SpinBox',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\SpinBox.py',
   'PYMODULE'),
  ('pyqtgraph.examples.Symbols',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\Symbols.py',
   'PYMODULE'),
  ('pyqtgraph.examples.TableWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\TableWidget.py',
   'PYMODULE'),
  ('pyqtgraph.examples.TreeWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\TreeWidget.py',
   'PYMODULE'),
  ('pyqtgraph.examples.VideoSpeedTest',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\VideoSpeedTest.py',
   'PYMODULE'),
  ('pyqtgraph.examples.VideoTemplate_generic',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\VideoTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ViewBox',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ViewBox.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ViewBoxFeatures',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ViewBoxFeatures.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ViewLimits',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ViewLimits.py',
   'PYMODULE'),
  ('pyqtgraph.examples.__main__',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\__main__.py',
   'PYMODULE'),
  ('pyqtgraph.examples._buildParamTypes',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\_buildParamTypes.py',
   'PYMODULE'),
  ('pyqtgraph.examples._paramtreecfg',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\_paramtreecfg.py',
   'PYMODULE'),
  ('pyqtgraph.examples.beeswarm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\beeswarm.py',
   'PYMODULE'),
  ('pyqtgraph.examples.colorMaps',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\colorMaps.py',
   'PYMODULE'),
  ('pyqtgraph.examples.colorMapsLinearized',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\colorMapsLinearized.py',
   'PYMODULE'),
  ('pyqtgraph.examples.console_exception_inspection',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\console_exception_inspection.py',
   'PYMODULE'),
  ('pyqtgraph.examples.contextMenu',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\contextMenu.py',
   'PYMODULE'),
  ('pyqtgraph.examples.crosshair',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\crosshair.py',
   'PYMODULE'),
  ('pyqtgraph.examples.customGraphicsItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\customGraphicsItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.customPlot',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\customPlot.py',
   'PYMODULE'),
  ('pyqtgraph.examples.designerExample',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\designerExample.py',
   'PYMODULE'),
  ('pyqtgraph.examples.dockarea',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\dockarea.py',
   'PYMODULE'),
  ('pyqtgraph.examples.exampleLoaderTemplate_generic',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\exampleLoaderTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.examples.fractal',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\fractal.py',
   'PYMODULE'),
  ('pyqtgraph.examples.glow',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\glow.py',
   'PYMODULE'),
  ('pyqtgraph.examples.hdf5',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\hdf5.py',
   'PYMODULE'),
  ('pyqtgraph.examples.histogram',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\histogram.py',
   'PYMODULE'),
  ('pyqtgraph.examples.imageAnalysis',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\imageAnalysis.py',
   'PYMODULE'),
  ('pyqtgraph.examples.infiniteline_performance',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\infiniteline_performance.py',
   'PYMODULE'),
  ('pyqtgraph.examples.isocurve',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\isocurve.py',
   'PYMODULE'),
  ('pyqtgraph.examples.jupyter_console_example',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\jupyter_console_example.py',
   'PYMODULE'),
  ('pyqtgraph.examples.linkedViews',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\linkedViews.py',
   'PYMODULE'),
  ('pyqtgraph.examples.logAxis',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\logAxis.py',
   'PYMODULE'),
  ('pyqtgraph.examples.multiplePlotSpeedTest',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\multiplePlotSpeedTest.py',
   'PYMODULE'),
  ('pyqtgraph.examples.multiprocess',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\multiprocess.py',
   'PYMODULE'),
  ('pyqtgraph.examples.optics',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\optics\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.examples.optics.pyoptic',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\optics\\pyoptic.py',
   'PYMODULE'),
  ('pyqtgraph.examples.optics_demos',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\optics_demos.py',
   'PYMODULE'),
  ('pyqtgraph.examples.parallelize',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\parallelize.py',
   'PYMODULE'),
  ('pyqtgraph.examples.parametertree',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\parametertree.py',
   'PYMODULE'),
  ('pyqtgraph.examples.relativity',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\relativity\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.examples.relativity.relativity',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\relativity\\relativity.py',
   'PYMODULE'),
  ('pyqtgraph.examples.relativity_demo',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\relativity_demo.py',
   'PYMODULE'),
  ('pyqtgraph.examples.scrollingPlots',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\scrollingPlots.py',
   'PYMODULE'),
  ('pyqtgraph.examples.syntax',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\syntax.py',
   'PYMODULE'),
  ('pyqtgraph.examples.template',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\template.py',
   'PYMODULE'),
  ('pyqtgraph.examples.test_examples',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\test_examples.py',
   'PYMODULE'),
  ('pyqtgraph.examples.text',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\text.py',
   'PYMODULE'),
  ('pyqtgraph.examples.utils',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\utils.py',
   'PYMODULE'),
  ('pyqtgraph.examples.verlet_chain',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\verlet_chain\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.examples.verlet_chain.chain',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\verlet_chain\\chain.py',
   'PYMODULE'),
  ('pyqtgraph.examples.verlet_chain.relax',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\verlet_chain\\relax.py',
   'PYMODULE'),
  ('pyqtgraph.examples.verlet_chain_demo',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\verlet_chain_demo.py',
   'PYMODULE'),
  ('pyqtgraph.exceptionHandling',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exceptionHandling.py',
   'PYMODULE'),
  ('pyqtgraph.exporters',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.CSVExporter',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\CSVExporter.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.Exporter',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\Exporter.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.HDF5Exporter',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\HDF5Exporter.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.ImageExporter',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\ImageExporter.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.Matplotlib',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\Matplotlib.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.PrintExporter',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\PrintExporter.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.SVGExporter',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\SVGExporter.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.Flowchart',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\Flowchart.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.FlowchartCtrlTemplate_generic',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\FlowchartCtrlTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.FlowchartGraphicsView',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\FlowchartGraphicsView.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.Node',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\Node.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.NodeLibrary',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\NodeLibrary.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.Terminal',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\Terminal.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library.Data',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\Data.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library.Display',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\Display.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library.Filters',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\Filters.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library.Operators',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\Operators.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library.common',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\common.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library.functions',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\functions.py',
   'PYMODULE'),
  ('pyqtgraph.frozenSupport',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\frozenSupport.py',
   'PYMODULE'),
  ('pyqtgraph.functions',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\functions.py',
   'PYMODULE'),
  ('pyqtgraph.functions_numba',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\functions_numba.py',
   'PYMODULE'),
  ('pyqtgraph.functions_qimage',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\functions_qimage.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ArrowItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ArrowItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.AxisItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\AxisItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.BarGraphItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\BarGraphItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ButtonItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ButtonItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ColorBarItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ColorBarItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.CurvePoint',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\CurvePoint.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.DateAxisItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\DateAxisItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ErrorBarItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ErrorBarItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.FillBetweenItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\FillBetweenItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GradientEditorItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GradientEditorItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GradientLegend',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GradientLegend.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GradientPresets',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GradientPresets.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GraphItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GraphicsItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GraphicsLayout',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsLayout.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GraphicsObject',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsObject.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GraphicsWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsWidget.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GraphicsWidgetAnchor',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsWidgetAnchor.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GridItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GridItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.HistogramLUTItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\HistogramLUTItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ImageItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ImageItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.InfiniteLine',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\InfiniteLine.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.IsocurveItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\IsocurveItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ItemGroup',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ItemGroup.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.LabelItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\LabelItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.LegendItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\LegendItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.LinearRegionItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\LinearRegionItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.MultiPlotItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\MultiPlotItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.NonUniformImage',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\NonUniformImage.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.PColorMeshItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PColorMeshItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.PlotCurveItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotCurveItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.PlotDataItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotDataItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.PlotItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotItem\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.PlotItem.PlotItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotItem\\PlotItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.PlotItem.plotConfigTemplate_generic',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotItem\\plotConfigTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ROI',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ROI.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ScaleBar',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ScaleBar.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ScatterPlotItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ScatterPlotItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.TargetItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\TargetItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.TextItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\TextItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.UIGraphicsItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\UIGraphicsItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.VTickGroup',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\VTickGroup.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ViewBox',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ViewBox.ViewBox',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\ViewBox.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ViewBox.ViewBoxMenu',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\ViewBoxMenu.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ViewBox.axisCtrlTemplate_generic',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\axisCtrlTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.icons',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.imageview',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\imageview\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.imageview.ImageView',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\imageview\\ImageView.py',
   'PYMODULE'),
  ('pyqtgraph.imageview.ImageViewTemplate_generic',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\imageview\\ImageViewTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.metaarray',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\metaarray\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.metaarray.MetaArray',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\metaarray\\MetaArray.py',
   'PYMODULE'),
  ('pyqtgraph.multiprocess',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\multiprocess\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.multiprocess.bootstrap',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\multiprocess\\bootstrap.py',
   'PYMODULE'),
  ('pyqtgraph.multiprocess.parallelizer',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\multiprocess\\parallelizer.py',
   'PYMODULE'),
  ('pyqtgraph.multiprocess.processes',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\multiprocess\\processes.py',
   'PYMODULE'),
  ('pyqtgraph.multiprocess.remoteproxy',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\multiprocess\\remoteproxy.py',
   'PYMODULE'),
  ('pyqtgraph.opengl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.GLGraphicsItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\GLGraphicsItem.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.GLViewWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\GLViewWidget.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.MeshData',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\MeshData.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.items',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.items.GLAxisItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLAxisItem.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.items.GLBarGraphItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLBarGraphItem.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.items.GLBoxItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLBoxItem.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.items.GLGradientLegendItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLGradientLegendItem.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.items.GLGraphItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLGraphItem.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.items.GLGridItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLGridItem.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.items.GLImageItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLImageItem.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.items.GLLinePlotItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLLinePlotItem.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.items.GLMeshItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLMeshItem.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.items.GLScatterPlotItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLScatterPlotItem.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.items.GLSurfacePlotItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLSurfacePlotItem.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.items.GLTextItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLTextItem.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.items.GLVolumeItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLVolumeItem.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.shaders',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\shaders.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.Parameter',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\Parameter.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.ParameterItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\ParameterItem.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.ParameterSystem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\ParameterSystem.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.ParameterTree',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\ParameterTree.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.SystemSolver',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\SystemSolver.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.interactive',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\interactive.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.action',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\action.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.actiongroup',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\actiongroup.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.basetypes',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\basetypes.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.bool',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\bool.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.calendar',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\calendar.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.checklist',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\checklist.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.color',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\color.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.colormap',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\colormap.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.colormaplut',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\colormaplut.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.file',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\file.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.font',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\font.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.list',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\list.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.numeric',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\numeric.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.pen',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\pen.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.progress',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\progress.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.qtenum',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\qtenum.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.slider',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\slider.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.str',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\str.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.text',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\text.py',
   'PYMODULE'),
  ('pyqtgraph.reload',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\reload.py',
   'PYMODULE'),
  ('pyqtgraph.units',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\units.py',
   'PYMODULE'),
  ('pyqtgraph.util',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.util.colorama',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\colorama\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.util.colorama.win32',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\colorama\\win32.py',
   'PYMODULE'),
  ('pyqtgraph.util.colorama.winterm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\colorama\\winterm.py',
   'PYMODULE'),
  ('pyqtgraph.util.cprint',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\cprint.py',
   'PYMODULE'),
  ('pyqtgraph.util.cupy_helper',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\cupy_helper.py',
   'PYMODULE'),
  ('pyqtgraph.util.garbage_collector',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\garbage_collector.py',
   'PYMODULE'),
  ('pyqtgraph.util.get_resolution',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\get_resolution.py',
   'PYMODULE'),
  ('pyqtgraph.util.glinfo',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\glinfo.py',
   'PYMODULE'),
  ('pyqtgraph.util.mutex',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\mutex.py',
   'PYMODULE'),
  ('pyqtgraph.util.numba_helper',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\numba_helper.py',
   'PYMODULE'),
  ('pyqtgraph.widgets',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.BusyCursor',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\BusyCursor.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.CheckTable',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\CheckTable.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ColorButton',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ColorButton.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ColorMapButton',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ColorMapButton.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ColorMapMenu',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ColorMapMenu.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ColorMapWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ColorMapWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ComboBox',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ComboBox.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.DataFilterWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\DataFilterWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.DataTreeWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\DataTreeWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.DiffTreeWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\DiffTreeWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.FeedbackButton',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\FeedbackButton.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.FileDialog',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\FileDialog.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.GradientWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\GradientWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.GraphicsLayoutWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\GraphicsLayoutWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.GraphicsView',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\GraphicsView.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.GroupBox',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\GroupBox.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.HistogramLUTWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\HistogramLUTWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.JoystickButton',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\JoystickButton.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.LayoutWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\LayoutWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.MatplotlibWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\MatplotlibWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.MultiPlotWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\MultiPlotWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.PathButton',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\PathButton.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.PenPreviewLabel',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\PenPreviewLabel.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.PlotWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\PlotWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ProgressDialog',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ProgressDialog.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.RawImageWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\RawImageWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.RemoteGraphicsView',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\RemoteGraphicsView.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ScatterPlotWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ScatterPlotWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.SpinBox',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\SpinBox.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.TableWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\TableWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.TreeWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\TreeWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ValueLabel',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ValueLabel.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.VerticalLabel',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\VerticalLabel.py',
   'PYMODULE'),
  ('queue', 'I:\\Program Files\\python\\python12\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'I:\\Program Files\\python\\python12\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'I:\\Program Files\\python\\python12\\Lib\\random.py', 'PYMODULE'),
  ('rlcompleter',
   'I:\\Program Files\\python\\python12\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy', 'I:\\Program Files\\python\\python12\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets',
   'I:\\Program Files\\python\\python12\\Lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'I:\\Program Files\\python\\python12\\Lib\\selectors.py',
   'PYMODULE'),
  ('serial',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\serial\\__init__.py',
   'PYMODULE'),
  ('serial.serialcli',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\serial\\serialcli.py',
   'PYMODULE'),
  ('serial.serialjava',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\serial\\serialjava.py',
   'PYMODULE'),
  ('serial.serialposix',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\serial\\serialposix.py',
   'PYMODULE'),
  ('serial.serialutil',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\serial\\serialutil.py',
   'PYMODULE'),
  ('serial.serialwin32',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\serial\\serialwin32.py',
   'PYMODULE'),
  ('serial.tools',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\serial\\tools\\__init__.py',
   'PYMODULE'),
  ('serial.tools.list_ports',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\serial\\tools\\list_ports.py',
   'PYMODULE'),
  ('serial.tools.list_ports_common',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\serial\\tools\\list_ports_common.py',
   'PYMODULE'),
  ('serial.tools.list_ports_linux',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\serial\\tools\\list_ports_linux.py',
   'PYMODULE'),
  ('serial.tools.list_ports_osx',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\serial\\tools\\list_ports_osx.py',
   'PYMODULE'),
  ('serial.tools.list_ports_posix',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\serial\\tools\\list_ports_posix.py',
   'PYMODULE'),
  ('serial.tools.list_ports_windows',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\serial\\tools\\list_ports_windows.py',
   'PYMODULE'),
  ('serial.win32',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\serial\\win32.py',
   'PYMODULE'),
  ('setuptools',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'I:\\Program Files\\python\\python12\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'I:\\Program Files\\python\\python12\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'I:\\Program Files\\python\\python12\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'I:\\Program Files\\python\\python12\\Lib\\site.py', 'PYMODULE'),
  ('socket', 'I:\\Program Files\\python\\python12\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'I:\\Program Files\\python\\python12\\Lib\\socketserver.py',
   'PYMODULE'),
  ('ssl', 'I:\\Program Files\\python\\python12\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics',
   'I:\\Program Files\\python\\python12\\Lib\\statistics.py',
   'PYMODULE'),
  ('string', 'I:\\Program Files\\python\\python12\\Lib\\string.py', 'PYMODULE'),
  ('stringprep',
   'I:\\Program Files\\python\\python12\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'I:\\Program Files\\python\\python12\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'I:\\Program Files\\python\\python12\\Lib\\sysconfig.py',
   'PYMODULE'),
  ('tarfile',
   'I:\\Program Files\\python\\python12\\Lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'I:\\Program Files\\python\\python12\\Lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'I:\\Program Files\\python\\python12\\Lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'I:\\Program Files\\python\\python12\\Lib\\threading.py',
   'PYMODULE'),
  ('token', 'I:\\Program Files\\python\\python12\\Lib\\token.py', 'PYMODULE'),
  ('tokenize',
   'I:\\Program Files\\python\\python12\\Lib\\tokenize.py',
   'PYMODULE'),
  ('tomllib',
   'I:\\Program Files\\python\\python12\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'I:\\Program Files\\python\\python12\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._re',
   'I:\\Program Files\\python\\python12\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('tomllib._types',
   'I:\\Program Files\\python\\python12\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tracemalloc',
   'I:\\Program Files\\python\\python12\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty', 'I:\\Program Files\\python\\python12\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'I:\\Program Files\\python\\python12\\Lib\\typing.py', 'PYMODULE'),
  ('unittest',
   'I:\\Program Files\\python\\python12\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'I:\\Program Files\\python\\python12\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'I:\\Program Files\\python\\python12\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'I:\\Program Files\\python\\python12\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'I:\\Program Files\\python\\python12\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'I:\\Program Files\\python\\python12\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'I:\\Program Files\\python\\python12\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'I:\\Program Files\\python\\python12\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'I:\\Program Files\\python\\python12\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'I:\\Program Files\\python\\python12\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'I:\\Program Files\\python\\python12\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'I:\\Program Files\\python\\python12\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'I:\\Program Files\\python\\python12\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'I:\\Program Files\\python\\python12\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'I:\\Program Files\\python\\python12\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'I:\\Program Files\\python\\python12\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'I:\\Program Files\\python\\python12\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('webbrowser',
   'I:\\Program Files\\python\\python12\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('xml',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.dom',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'I:\\Program Files\\python\\python12\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'I:\\Program Files\\python\\python12\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'I:\\Program Files\\python\\python12\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'I:\\Program Files\\python\\python12\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'I:\\Program Files\\python\\python12\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport',
   'I:\\Program Files\\python\\python12\\Lib\\zipimport.py',
   'PYMODULE')])
