(['O:\\python\\shangweiji_test\\main.py'],
 ['O:\\python\\shangweiji_test'],
 ['pyqtgraph',
  'pyqtgraph.GraphicsScene',
  'pyqtgraph.GraphicsScene.GraphicsScene',
  'pyqtgraph.GraphicsScene.exportDialog',
  'pyqtgraph.GraphicsScene.exportDialogTemplate_generic',
  'pyqtgraph.GraphicsScene.mouseEvents',
  'pyqtgraph.PlotData',
  'pyqtgraph.Point',
  'pyqtgraph.Qt',
  'pyqtgraph.Qt.QtCore',
  'pyqtgraph.Qt.QtGui',
  'pyqtgraph.Qt.QtWidgets',
  'pyqtgraph.Qt.compat',
  'pyqtgraph.Qt.internals',
  'pyqtgraph.SRTTransform',
  'pyqtgraph.SRTTransform3D',
  'pyqtgraph.SignalProxy',
  'pyqtgraph.ThreadsafeTimer',
  'pyqtgraph.Transform3D',
  'pyqtgraph.Vector',
  'pyqtgraph.WidgetGroup',
  'pyqtgraph.canvas',
  'pyqtgraph.canvas.Canvas',
  'pyqtgraph.canvas.CanvasItem',
  'pyqtgraph.canvas.CanvasManager',
  'pyqtgraph.canvas.CanvasTemplate_generic',
  'pyqtgraph.canvas.TransformGuiTemplate_generic',
  'pyqtgraph.colormap',
  'pyqtgraph.colors',
  'pyqtgraph.colors.palette',
  'pyqtgraph.configfile',
  'pyqtgraph.console',
  'pyqtgraph.console.CmdInput',
  'pyqtgraph.console.Console',
  'pyqtgraph.console.exception_widget',
  'pyqtgraph.console.repl_widget',
  'pyqtgraph.console.stackwidget',
  'pyqtgraph.debug',
  'pyqtgraph.dockarea',
  'pyqtgraph.dockarea.Container',
  'pyqtgraph.dockarea.Dock',
  'pyqtgraph.dockarea.DockArea',
  'pyqtgraph.dockarea.DockDrop',
  'pyqtgraph.examples',
  'pyqtgraph.examples.Arrow',
  'pyqtgraph.examples.AxisItem_label_overlap',
  'pyqtgraph.examples.BarGraphItem',
  'pyqtgraph.examples.CLIexample',
  'pyqtgraph.examples.ColorBarItem',
  'pyqtgraph.examples.ColorButton',
  'pyqtgraph.examples.ColorGradientPlots',
  'pyqtgraph.examples.ConsoleWidget',
  'pyqtgraph.examples.CustomGraphItem',
  'pyqtgraph.examples.DataSlicing',
  'pyqtgraph.examples.DataTreeWidget',
  'pyqtgraph.examples.DateAxisItem',
  'pyqtgraph.examples.DateAxisItem_QtDesigner',
  'pyqtgraph.examples.DiffTreeWidget',
  'pyqtgraph.examples.Draw',
  'pyqtgraph.examples.ErrorBarItem',
  'pyqtgraph.examples.ExampleApp',
  'pyqtgraph.examples.FillBetweenItem',
  'pyqtgraph.examples.Flowchart',
  'pyqtgraph.examples.FlowchartCustomNode',
  'pyqtgraph.examples.GLBarGraphItem',
  'pyqtgraph.examples.GLGradientLegendItem',
  'pyqtgraph.examples.GLGraphItem',
  'pyqtgraph.examples.GLImageItem',
  'pyqtgraph.examples.GLIsosurface',
  'pyqtgraph.examples.GLLinePlotItem',
  'pyqtgraph.examples.GLMeshItem',
  'pyqtgraph.examples.GLPainterItem',
  'pyqtgraph.examples.GLScatterPlotItem',
  'pyqtgraph.examples.GLSurfacePlot',
  'pyqtgraph.examples.GLTextItem',
  'pyqtgraph.examples.GLViewWidget',
  'pyqtgraph.examples.GLVolumeItem',
  'pyqtgraph.examples.GLshaders',
  'pyqtgraph.examples.GradientEditor',
  'pyqtgraph.examples.GradientWidget',
  'pyqtgraph.examples.GraphItem',
  'pyqtgraph.examples.GraphicsLayout',
  'pyqtgraph.examples.GraphicsScene',
  'pyqtgraph.examples.HistogramLUT',
  'pyqtgraph.examples.ImageItem',
  'pyqtgraph.examples.ImageView',
  'pyqtgraph.examples.InfiniteLine',
  'pyqtgraph.examples.InteractiveParameter',
  'pyqtgraph.examples.JoystickButton',
  'pyqtgraph.examples.Legend',
  'pyqtgraph.examples.LogPlotTest',
  'pyqtgraph.examples.MatrixDisplayExample',
  'pyqtgraph.examples.MouseSelection',
  'pyqtgraph.examples.MultiDataPlot',
  'pyqtgraph.examples.MultiPlotSpeedTest',
  'pyqtgraph.examples.MultiplePlotAxes',
  'pyqtgraph.examples.NonUniformImage',
  'pyqtgraph.examples.PColorMeshItem',
  'pyqtgraph.examples.PanningPlot',
  'pyqtgraph.examples.PlotAutoRange',
  'pyqtgraph.examples.PlotSpeedTest',
  'pyqtgraph.examples.PlotWidget',
  'pyqtgraph.examples.Plotting',
  'pyqtgraph.examples.ProgressDialog',
  'pyqtgraph.examples.ROIExamples',
  'pyqtgraph.examples.ROItypes',
  'pyqtgraph.examples.RemoteGraphicsView',
  'pyqtgraph.examples.RemoteSpeedTest',
  'pyqtgraph.examples.RunExampleApp',
  'pyqtgraph.examples.ScaleBar',
  'pyqtgraph.examples.ScatterPlot',
  'pyqtgraph.examples.ScatterPlotSpeedTest',
  'pyqtgraph.examples.ScatterPlotWidget',
  'pyqtgraph.examples.SimplePlot',
  'pyqtgraph.examples.SpinBox',
  'pyqtgraph.examples.Symbols',
  'pyqtgraph.examples.TableWidget',
  'pyqtgraph.examples.TreeWidget',
  'pyqtgraph.examples.VideoSpeedTest',
  'pyqtgraph.examples.VideoTemplate_generic',
  'pyqtgraph.examples.ViewBox',
  'pyqtgraph.examples.ViewBoxFeatures',
  'pyqtgraph.examples.ViewLimits',
  'pyqtgraph.examples.__main__',
  'pyqtgraph.examples._buildParamTypes',
  'pyqtgraph.examples._paramtreecfg',
  'pyqtgraph.examples.beeswarm',
  'pyqtgraph.examples.colorMaps',
  'pyqtgraph.examples.colorMapsLinearized',
  'pyqtgraph.examples.console_exception_inspection',
  'pyqtgraph.examples.contextMenu',
  'pyqtgraph.examples.crosshair',
  'pyqtgraph.examples.customGraphicsItem',
  'pyqtgraph.examples.customPlot',
  'pyqtgraph.examples.designerExample',
  'pyqtgraph.examples.dockarea',
  'pyqtgraph.examples.exampleLoaderTemplate_generic',
  'pyqtgraph.examples.fractal',
  'pyqtgraph.examples.glow',
  'pyqtgraph.examples.hdf5',
  'pyqtgraph.examples.histogram',
  'pyqtgraph.examples.imageAnalysis',
  'pyqtgraph.examples.infiniteline_performance',
  'pyqtgraph.examples.isocurve',
  'pyqtgraph.examples.jupyter_console_example',
  'pyqtgraph.examples.linkedViews',
  'pyqtgraph.examples.logAxis',
  'pyqtgraph.examples.multiplePlotSpeedTest',
  'pyqtgraph.examples.multiprocess',
  'pyqtgraph.examples.optics',
  'pyqtgraph.examples.optics.pyoptic',
  'pyqtgraph.examples.optics_demos',
  'pyqtgraph.examples.parallelize',
  'pyqtgraph.examples.parametertree',
  'pyqtgraph.examples.relativity',
  'pyqtgraph.examples.relativity.relativity',
  'pyqtgraph.examples.relativity_demo',
  'pyqtgraph.examples.scrollingPlots',
  'pyqtgraph.examples.syntax',
  'pyqtgraph.examples.template',
  'pyqtgraph.examples.test_examples',
  'pyqtgraph.examples.text',
  'pyqtgraph.examples.utils',
  'pyqtgraph.examples.verlet_chain',
  'pyqtgraph.examples.verlet_chain.chain',
  'pyqtgraph.examples.verlet_chain.relax',
  'pyqtgraph.examples.verlet_chain_demo',
  'pyqtgraph.exceptionHandling',
  'pyqtgraph.exporters',
  'pyqtgraph.exporters.CSVExporter',
  'pyqtgraph.exporters.Exporter',
  'pyqtgraph.exporters.HDF5Exporter',
  'pyqtgraph.exporters.ImageExporter',
  'pyqtgraph.exporters.Matplotlib',
  'pyqtgraph.exporters.PrintExporter',
  'pyqtgraph.exporters.SVGExporter',
  'pyqtgraph.flowchart',
  'pyqtgraph.flowchart.Flowchart',
  'pyqtgraph.flowchart.FlowchartCtrlTemplate_generic',
  'pyqtgraph.flowchart.FlowchartGraphicsView',
  'pyqtgraph.flowchart.Node',
  'pyqtgraph.flowchart.NodeLibrary',
  'pyqtgraph.flowchart.Terminal',
  'pyqtgraph.flowchart.library',
  'pyqtgraph.flowchart.library.Data',
  'pyqtgraph.flowchart.library.Display',
  'pyqtgraph.flowchart.library.Filters',
  'pyqtgraph.flowchart.library.Operators',
  'pyqtgraph.flowchart.library.common',
  'pyqtgraph.flowchart.library.functions',
  'pyqtgraph.frozenSupport',
  'pyqtgraph.functions',
  'pyqtgraph.functions_numba',
  'pyqtgraph.functions_qimage',
  'pyqtgraph.graphicsItems',
  'pyqtgraph.graphicsItems.ArrowItem',
  'pyqtgraph.graphicsItems.AxisItem',
  'pyqtgraph.graphicsItems.BarGraphItem',
  'pyqtgraph.graphicsItems.ButtonItem',
  'pyqtgraph.graphicsItems.ColorBarItem',
  'pyqtgraph.graphicsItems.CurvePoint',
  'pyqtgraph.graphicsItems.DateAxisItem',
  'pyqtgraph.graphicsItems.ErrorBarItem',
  'pyqtgraph.graphicsItems.FillBetweenItem',
  'pyqtgraph.graphicsItems.GradientEditorItem',
  'pyqtgraph.graphicsItems.GradientLegend',
  'pyqtgraph.graphicsItems.GradientPresets',
  'pyqtgraph.graphicsItems.GraphItem',
  'pyqtgraph.graphicsItems.GraphicsItem',
  'pyqtgraph.graphicsItems.GraphicsLayout',
  'pyqtgraph.graphicsItems.GraphicsObject',
  'pyqtgraph.graphicsItems.GraphicsWidget',
  'pyqtgraph.graphicsItems.GraphicsWidgetAnchor',
  'pyqtgraph.graphicsItems.GridItem',
  'pyqtgraph.graphicsItems.HistogramLUTItem',
  'pyqtgraph.graphicsItems.ImageItem',
  'pyqtgraph.graphicsItems.InfiniteLine',
  'pyqtgraph.graphicsItems.IsocurveItem',
  'pyqtgraph.graphicsItems.ItemGroup',
  'pyqtgraph.graphicsItems.LabelItem',
  'pyqtgraph.graphicsItems.LegendItem',
  'pyqtgraph.graphicsItems.LinearRegionItem',
  'pyqtgraph.graphicsItems.MultiPlotItem',
  'pyqtgraph.graphicsItems.NonUniformImage',
  'pyqtgraph.graphicsItems.PColorMeshItem',
  'pyqtgraph.graphicsItems.PlotCurveItem',
  'pyqtgraph.graphicsItems.PlotDataItem',
  'pyqtgraph.graphicsItems.PlotItem',
  'pyqtgraph.graphicsItems.PlotItem.PlotItem',
  'pyqtgraph.graphicsItems.PlotItem.plotConfigTemplate_generic',
  'pyqtgraph.graphicsItems.ROI',
  'pyqtgraph.graphicsItems.ScaleBar',
  'pyqtgraph.graphicsItems.ScatterPlotItem',
  'pyqtgraph.graphicsItems.TargetItem',
  'pyqtgraph.graphicsItems.TextItem',
  'pyqtgraph.graphicsItems.UIGraphicsItem',
  'pyqtgraph.graphicsItems.VTickGroup',
  'pyqtgraph.graphicsItems.ViewBox',
  'pyqtgraph.graphicsItems.ViewBox.ViewBox',
  'pyqtgraph.graphicsItems.ViewBox.ViewBoxMenu',
  'pyqtgraph.graphicsItems.ViewBox.axisCtrlTemplate_generic',
  'pyqtgraph.icons',
  'pyqtgraph.imageview',
  'pyqtgraph.imageview.ImageView',
  'pyqtgraph.imageview.ImageViewTemplate_generic',
  'pyqtgraph.metaarray',
  'pyqtgraph.metaarray.MetaArray',
  'pyqtgraph.multiprocess',
  'pyqtgraph.multiprocess.bootstrap',
  'pyqtgraph.multiprocess.parallelizer',
  'pyqtgraph.multiprocess.processes',
  'pyqtgraph.multiprocess.remoteproxy',
  'pyqtgraph.parametertree',
  'pyqtgraph.parametertree.Parameter',
  'pyqtgraph.parametertree.ParameterItem',
  'pyqtgraph.parametertree.ParameterSystem',
  'pyqtgraph.parametertree.ParameterTree',
  'pyqtgraph.parametertree.SystemSolver',
  'pyqtgraph.parametertree.interactive',
  'pyqtgraph.parametertree.parameterTypes',
  'pyqtgraph.parametertree.parameterTypes.action',
  'pyqtgraph.parametertree.parameterTypes.actiongroup',
  'pyqtgraph.parametertree.parameterTypes.basetypes',
  'pyqtgraph.parametertree.parameterTypes.bool',
  'pyqtgraph.parametertree.parameterTypes.calendar',
  'pyqtgraph.parametertree.parameterTypes.checklist',
  'pyqtgraph.parametertree.parameterTypes.color',
  'pyqtgraph.parametertree.parameterTypes.colormap',
  'pyqtgraph.parametertree.parameterTypes.colormaplut',
  'pyqtgraph.parametertree.parameterTypes.file',
  'pyqtgraph.parametertree.parameterTypes.font',
  'pyqtgraph.parametertree.parameterTypes.list',
  'pyqtgraph.parametertree.parameterTypes.numeric',
  'pyqtgraph.parametertree.parameterTypes.pen',
  'pyqtgraph.parametertree.parameterTypes.progress',
  'pyqtgraph.parametertree.parameterTypes.qtenum',
  'pyqtgraph.parametertree.parameterTypes.slider',
  'pyqtgraph.parametertree.parameterTypes.str',
  'pyqtgraph.parametertree.parameterTypes.text',
  'pyqtgraph.reload',
  'pyqtgraph.units',
  'pyqtgraph.util',
  'pyqtgraph.util.colorama',
  'pyqtgraph.util.colorama.win32',
  'pyqtgraph.util.colorama.winterm',
  'pyqtgraph.util.cprint',
  'pyqtgraph.util.cupy_helper',
  'pyqtgraph.util.garbage_collector',
  'pyqtgraph.util.get_resolution',
  'pyqtgraph.util.glinfo',
  'pyqtgraph.util.mutex',
  'pyqtgraph.util.numba_helper',
  'pyqtgraph.widgets',
  'pyqtgraph.widgets.BusyCursor',
  'pyqtgraph.widgets.CheckTable',
  'pyqtgraph.widgets.ColorButton',
  'pyqtgraph.widgets.ColorMapButton',
  'pyqtgraph.widgets.ColorMapMenu',
  'pyqtgraph.widgets.ColorMapWidget',
  'pyqtgraph.widgets.ComboBox',
  'pyqtgraph.widgets.DataFilterWidget',
  'pyqtgraph.widgets.DataTreeWidget',
  'pyqtgraph.widgets.DiffTreeWidget',
  'pyqtgraph.widgets.FeedbackButton',
  'pyqtgraph.widgets.FileDialog',
  'pyqtgraph.widgets.GradientWidget',
  'pyqtgraph.widgets.GraphicsLayoutWidget',
  'pyqtgraph.widgets.GraphicsView',
  'pyqtgraph.widgets.GroupBox',
  'pyqtgraph.widgets.HistogramLUTWidget',
  'pyqtgraph.widgets.JoystickButton',
  'pyqtgraph.widgets.LayoutWidget',
  'pyqtgraph.widgets.MatplotlibWidget',
  'pyqtgraph.widgets.MultiPlotWidget',
  'pyqtgraph.widgets.PathButton',
  'pyqtgraph.widgets.PenPreviewLabel',
  'pyqtgraph.widgets.PlotWidget',
  'pyqtgraph.widgets.ProgressDialog',
  'pyqtgraph.widgets.RawImageWidget',
  'pyqtgraph.widgets.RemoteGraphicsView',
  'pyqtgraph.widgets.ScatterPlotWidget',
  'pyqtgraph.widgets.SpinBox',
  'pyqtgraph.widgets.TableWidget',
  'pyqtgraph.widgets.TreeWidget',
  'pyqtgraph.widgets.ValueLabel',
  'pyqtgraph.widgets.VerticalLabel'],
 [('O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_pyinstaller',
   0),
  ('O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('pyqtgraph-0.13.7.dist-info\\INSTALLER',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph-0.13.7.dist-info\\INSTALLER',
   'DATA'),
  ('pyqtgraph-0.13.7.dist-info\\LICENSE.txt',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph-0.13.7.dist-info\\LICENSE.txt',
   'DATA'),
  ('pyqtgraph-0.13.7.dist-info\\METADATA',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph-0.13.7.dist-info\\METADATA',
   'DATA'),
  ('pyqtgraph-0.13.7.dist-info\\RECORD',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph-0.13.7.dist-info\\RECORD',
   'DATA'),
  ('pyqtgraph-0.13.7.dist-info\\REQUESTED',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph-0.13.7.dist-info\\REQUESTED',
   'DATA'),
  ('pyqtgraph-0.13.7.dist-info\\WHEEL',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph-0.13.7.dist-info\\WHEEL',
   'DATA'),
  ('pyqtgraph-0.13.7.dist-info\\top_level.txt',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph-0.13.7.dist-info\\top_level.txt',
   'DATA'),
  ('pyqtgraph\\GraphicsScene\\GraphicsScene.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\GraphicsScene.py',
   'DATA'),
  ('pyqtgraph\\GraphicsScene\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\__init__.py',
   'DATA'),
  ('pyqtgraph\\GraphicsScene\\exportDialog.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\exportDialog.py',
   'DATA'),
  ('pyqtgraph\\GraphicsScene\\exportDialogTemplate_generic.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\exportDialogTemplate_generic.py',
   'DATA'),
  ('pyqtgraph\\GraphicsScene\\mouseEvents.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\mouseEvents.py',
   'DATA'),
  ('pyqtgraph\\PlotData.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\PlotData.py',
   'DATA'),
  ('pyqtgraph\\Point.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Point.py',
   'DATA'),
  ('pyqtgraph\\Qt\\QtCore\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtCore\\__init__.py',
   'DATA'),
  ('pyqtgraph\\Qt\\QtCore\\__init__.pyi',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtCore\\__init__.pyi',
   'DATA'),
  ('pyqtgraph\\Qt\\QtGui\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtGui\\__init__.py',
   'DATA'),
  ('pyqtgraph\\Qt\\QtGui\\__init__.pyi',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtGui\\__init__.pyi',
   'DATA'),
  ('pyqtgraph\\Qt\\QtSvg.pyi',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtSvg.pyi',
   'DATA'),
  ('pyqtgraph\\Qt\\QtTest.pyi',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtTest.pyi',
   'DATA'),
  ('pyqtgraph\\Qt\\QtWidgets\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtWidgets\\__init__.py',
   'DATA'),
  ('pyqtgraph\\Qt\\QtWidgets\\__init__.pyi',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtWidgets\\__init__.pyi',
   'DATA'),
  ('pyqtgraph\\Qt\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\__init__.py',
   'DATA'),
  ('pyqtgraph\\Qt\\__init__.pyi',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\__init__.pyi',
   'DATA'),
  ('pyqtgraph\\Qt\\compat\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\compat\\__init__.py',
   'DATA'),
  ('pyqtgraph\\Qt\\internals.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\internals.py',
   'DATA'),
  ('pyqtgraph\\SRTTransform.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\SRTTransform.py',
   'DATA'),
  ('pyqtgraph\\SRTTransform3D.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\SRTTransform3D.py',
   'DATA'),
  ('pyqtgraph\\SignalProxy.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\SignalProxy.py',
   'DATA'),
  ('pyqtgraph\\ThreadsafeTimer.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\ThreadsafeTimer.py',
   'DATA'),
  ('pyqtgraph\\Transform3D.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Transform3D.py',
   'DATA'),
  ('pyqtgraph\\Vector.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Vector.py',
   'DATA'),
  ('pyqtgraph\\WidgetGroup.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\WidgetGroup.py',
   'DATA'),
  ('pyqtgraph\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\__init__.py',
   'DATA'),
  ('pyqtgraph\\canvas\\Canvas.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\Canvas.py',
   'DATA'),
  ('pyqtgraph\\canvas\\CanvasItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\CanvasItem.py',
   'DATA'),
  ('pyqtgraph\\canvas\\CanvasManager.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\CanvasManager.py',
   'DATA'),
  ('pyqtgraph\\canvas\\CanvasTemplate_generic.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\CanvasTemplate_generic.py',
   'DATA'),
  ('pyqtgraph\\canvas\\TransformGuiTemplate_generic.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\TransformGuiTemplate_generic.py',
   'DATA'),
  ('pyqtgraph\\canvas\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\__init__.py',
   'DATA'),
  ('pyqtgraph\\colormap.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colormap.py',
   'DATA'),
  ('pyqtgraph\\colors\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\__init__.py',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CC-BY license - applies to CET color map data.txt',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CC-BY '
   'license - applies to CET color map data.txt',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CC0 legal code - applies to virids, magma, '
   'plasma, inferno and cividis.txt',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CC0 '
   'legal code - applies to virids, magma, plasma, inferno and cividis.txt',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C1s.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C1s.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C2.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C2s.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C2s.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C3.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C3.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C3s.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C3s.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C4.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C4.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C4s.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C4s.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C5.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C5.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C5s.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C5s.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C6.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C6.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C6s.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C6s.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C7.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C7.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C7s.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C7s.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBC1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBC1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBC2.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBC2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBD1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBD1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBL1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBL1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBL2.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBL2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBTC1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBTC1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBTC2.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBTC2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBTD1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBTD1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBTL1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBTL1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBTL2.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBTL2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D10.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D10.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D11.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D11.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D12.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D12.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D13.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D13.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D1A.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D1A.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D2.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D3.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D3.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D4.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D4.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D6.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D6.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D7.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D7.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D8.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D8.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D9.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D9.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-I1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-I1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-I2.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-I2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-I3.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-I3.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L10.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L10.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L11.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L11.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L12.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L12.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L13.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L13.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L14.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L14.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L15.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L15.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L16.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L16.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L17.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L17.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L18.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L18.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L19.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L19.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L2.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L3.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L3.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L4.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L4.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L5.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L5.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L6.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L6.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L7.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L7.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L8.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L8.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L9.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L9.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-R1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-R1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-R2.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-R2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-R3.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-R3.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-R4.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-R4.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\PAL-relaxed.hex',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\PAL-relaxed.hex',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\PAL-relaxed_bright.hex',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\PAL-relaxed_bright.hex',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\cividis.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\cividis.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\inferno.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\inferno.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\magma.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\magma.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\plasma.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\plasma.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\turbo.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\turbo.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\viridis.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\viridis.csv',
   'DATA'),
  ('pyqtgraph\\colors\\palette.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\palette.py',
   'DATA'),
  ('pyqtgraph\\configfile.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\configfile.py',
   'DATA'),
  ('pyqtgraph\\console\\CmdInput.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\CmdInput.py',
   'DATA'),
  ('pyqtgraph\\console\\Console.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\Console.py',
   'DATA'),
  ('pyqtgraph\\console\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\__init__.py',
   'DATA'),
  ('pyqtgraph\\console\\exception_widget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\exception_widget.py',
   'DATA'),
  ('pyqtgraph\\console\\repl_widget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\repl_widget.py',
   'DATA'),
  ('pyqtgraph\\console\\stackwidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\stackwidget.py',
   'DATA'),
  ('pyqtgraph\\debug.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\debug.py',
   'DATA'),
  ('pyqtgraph\\dockarea\\Container.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\dockarea\\Container.py',
   'DATA'),
  ('pyqtgraph\\dockarea\\Dock.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\dockarea\\Dock.py',
   'DATA'),
  ('pyqtgraph\\dockarea\\DockArea.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\dockarea\\DockArea.py',
   'DATA'),
  ('pyqtgraph\\dockarea\\DockDrop.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\dockarea\\DockDrop.py',
   'DATA'),
  ('pyqtgraph\\dockarea\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\dockarea\\__init__.py',
   'DATA'),
  ('pyqtgraph\\examples\\Arrow.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\Arrow.py',
   'DATA'),
  ('pyqtgraph\\examples\\AxisItem_label_overlap.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\AxisItem_label_overlap.py',
   'DATA'),
  ('pyqtgraph\\examples\\BarGraphItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\BarGraphItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\CLIexample.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\CLIexample.py',
   'DATA'),
  ('pyqtgraph\\examples\\ColorBarItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ColorBarItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\ColorButton.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ColorButton.py',
   'DATA'),
  ('pyqtgraph\\examples\\ColorGradientPlots.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ColorGradientPlots.py',
   'DATA'),
  ('pyqtgraph\\examples\\ConsoleWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ConsoleWidget.py',
   'DATA'),
  ('pyqtgraph\\examples\\CustomGraphItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\CustomGraphItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\DataSlicing.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\DataSlicing.py',
   'DATA'),
  ('pyqtgraph\\examples\\DataTreeWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\DataTreeWidget.py',
   'DATA'),
  ('pyqtgraph\\examples\\DateAxisItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\DateAxisItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\DateAxisItem_QtDesigner.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\DateAxisItem_QtDesigner.py',
   'DATA'),
  ('pyqtgraph\\examples\\DiffTreeWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\DiffTreeWidget.py',
   'DATA'),
  ('pyqtgraph\\examples\\Draw.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\Draw.py',
   'DATA'),
  ('pyqtgraph\\examples\\ErrorBarItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ErrorBarItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\ExampleApp.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ExampleApp.py',
   'DATA'),
  ('pyqtgraph\\examples\\FillBetweenItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\FillBetweenItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\Flowchart.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\Flowchart.py',
   'DATA'),
  ('pyqtgraph\\examples\\FlowchartCustomNode.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\FlowchartCustomNode.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLBarGraphItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLBarGraphItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLGradientLegendItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLGradientLegendItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLGraphItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLGraphItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLImageItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLImageItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLIsosurface.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLIsosurface.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLLinePlotItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLLinePlotItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLMeshItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLMeshItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLPainterItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLPainterItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLScatterPlotItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLScatterPlotItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLSurfacePlot.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLSurfacePlot.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLTextItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLTextItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLViewWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLViewWidget.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLVolumeItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLVolumeItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLshaders.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLshaders.py',
   'DATA'),
  ('pyqtgraph\\examples\\GradientEditor.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GradientEditor.py',
   'DATA'),
  ('pyqtgraph\\examples\\GradientWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GradientWidget.py',
   'DATA'),
  ('pyqtgraph\\examples\\GraphItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GraphItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GraphicsLayout.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GraphicsLayout.py',
   'DATA'),
  ('pyqtgraph\\examples\\GraphicsScene.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GraphicsScene.py',
   'DATA'),
  ('pyqtgraph\\examples\\HistogramLUT.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\HistogramLUT.py',
   'DATA'),
  ('pyqtgraph\\examples\\ImageItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ImageItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\ImageView.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ImageView.py',
   'DATA'),
  ('pyqtgraph\\examples\\InfiniteLine.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\InfiniteLine.py',
   'DATA'),
  ('pyqtgraph\\examples\\InteractiveParameter.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\InteractiveParameter.py',
   'DATA'),
  ('pyqtgraph\\examples\\JoystickButton.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\JoystickButton.py',
   'DATA'),
  ('pyqtgraph\\examples\\Legend.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\Legend.py',
   'DATA'),
  ('pyqtgraph\\examples\\LogPlotTest.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\LogPlotTest.py',
   'DATA'),
  ('pyqtgraph\\examples\\MatrixDisplayExample.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\MatrixDisplayExample.py',
   'DATA'),
  ('pyqtgraph\\examples\\MouseSelection.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\MouseSelection.py',
   'DATA'),
  ('pyqtgraph\\examples\\MultiDataPlot.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\MultiDataPlot.py',
   'DATA'),
  ('pyqtgraph\\examples\\MultiPlotSpeedTest.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\MultiPlotSpeedTest.py',
   'DATA'),
  ('pyqtgraph\\examples\\MultiplePlotAxes.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\MultiplePlotAxes.py',
   'DATA'),
  ('pyqtgraph\\examples\\NonUniformImage.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\NonUniformImage.py',
   'DATA'),
  ('pyqtgraph\\examples\\PColorMeshItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\PColorMeshItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\PanningPlot.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\PanningPlot.py',
   'DATA'),
  ('pyqtgraph\\examples\\PlotAutoRange.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\PlotAutoRange.py',
   'DATA'),
  ('pyqtgraph\\examples\\PlotSpeedTest.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\PlotSpeedTest.py',
   'DATA'),
  ('pyqtgraph\\examples\\PlotWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\PlotWidget.py',
   'DATA'),
  ('pyqtgraph\\examples\\Plotting.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\Plotting.py',
   'DATA'),
  ('pyqtgraph\\examples\\ProgressDialog.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ProgressDialog.py',
   'DATA'),
  ('pyqtgraph\\examples\\ROIExamples.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ROIExamples.py',
   'DATA'),
  ('pyqtgraph\\examples\\ROItypes.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ROItypes.py',
   'DATA'),
  ('pyqtgraph\\examples\\RemoteGraphicsView.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\RemoteGraphicsView.py',
   'DATA'),
  ('pyqtgraph\\examples\\RemoteSpeedTest.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\RemoteSpeedTest.py',
   'DATA'),
  ('pyqtgraph\\examples\\RunExampleApp.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\RunExampleApp.py',
   'DATA'),
  ('pyqtgraph\\examples\\ScaleBar.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ScaleBar.py',
   'DATA'),
  ('pyqtgraph\\examples\\ScatterPlot.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ScatterPlot.py',
   'DATA'),
  ('pyqtgraph\\examples\\ScatterPlotSpeedTest.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ScatterPlotSpeedTest.py',
   'DATA'),
  ('pyqtgraph\\examples\\ScatterPlotWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ScatterPlotWidget.py',
   'DATA'),
  ('pyqtgraph\\examples\\SimplePlot.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\SimplePlot.py',
   'DATA'),
  ('pyqtgraph\\examples\\SpinBox.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\SpinBox.py',
   'DATA'),
  ('pyqtgraph\\examples\\Symbols.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\Symbols.py',
   'DATA'),
  ('pyqtgraph\\examples\\TableWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\TableWidget.py',
   'DATA'),
  ('pyqtgraph\\examples\\TreeWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\TreeWidget.py',
   'DATA'),
  ('pyqtgraph\\examples\\VideoSpeedTest.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\VideoSpeedTest.py',
   'DATA'),
  ('pyqtgraph\\examples\\VideoTemplate_generic.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\VideoTemplate_generic.py',
   'DATA'),
  ('pyqtgraph\\examples\\ViewBox.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ViewBox.py',
   'DATA'),
  ('pyqtgraph\\examples\\ViewBoxFeatures.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ViewBoxFeatures.py',
   'DATA'),
  ('pyqtgraph\\examples\\ViewLimits.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ViewLimits.py',
   'DATA'),
  ('pyqtgraph\\examples\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\__init__.py',
   'DATA'),
  ('pyqtgraph\\examples\\__main__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\__main__.py',
   'DATA'),
  ('pyqtgraph\\examples\\_buildParamTypes.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\_buildParamTypes.py',
   'DATA'),
  ('pyqtgraph\\examples\\_paramtreecfg.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\_paramtreecfg.py',
   'DATA'),
  ('pyqtgraph\\examples\\beeswarm.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\beeswarm.py',
   'DATA'),
  ('pyqtgraph\\examples\\colorMaps.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\colorMaps.py',
   'DATA'),
  ('pyqtgraph\\examples\\colorMapsLinearized.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\colorMapsLinearized.py',
   'DATA'),
  ('pyqtgraph\\examples\\console_exception_inspection.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\console_exception_inspection.py',
   'DATA'),
  ('pyqtgraph\\examples\\contextMenu.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\contextMenu.py',
   'DATA'),
  ('pyqtgraph\\examples\\crosshair.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\crosshair.py',
   'DATA'),
  ('pyqtgraph\\examples\\customGraphicsItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\customGraphicsItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\customPlot.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\customPlot.py',
   'DATA'),
  ('pyqtgraph\\examples\\cx_freeze\\plotTest.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\cx_freeze\\plotTest.py',
   'DATA'),
  ('pyqtgraph\\examples\\cx_freeze\\setup.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\cx_freeze\\setup.py',
   'DATA'),
  ('pyqtgraph\\examples\\designerExample.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\designerExample.py',
   'DATA'),
  ('pyqtgraph\\examples\\dockarea.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\dockarea.py',
   'DATA'),
  ('pyqtgraph\\examples\\exampleLoaderTemplate_generic.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\exampleLoaderTemplate_generic.py',
   'DATA'),
  ('pyqtgraph\\examples\\fractal.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\fractal.py',
   'DATA'),
  ('pyqtgraph\\examples\\glow.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\glow.py',
   'DATA'),
  ('pyqtgraph\\examples\\hdf5.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\hdf5.py',
   'DATA'),
  ('pyqtgraph\\examples\\histogram.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\histogram.py',
   'DATA'),
  ('pyqtgraph\\examples\\imageAnalysis.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\imageAnalysis.py',
   'DATA'),
  ('pyqtgraph\\examples\\infiniteline_performance.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\infiniteline_performance.py',
   'DATA'),
  ('pyqtgraph\\examples\\isocurve.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\isocurve.py',
   'DATA'),
  ('pyqtgraph\\examples\\jupyter_console_example.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\jupyter_console_example.py',
   'DATA'),
  ('pyqtgraph\\examples\\linkedViews.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\linkedViews.py',
   'DATA'),
  ('pyqtgraph\\examples\\logAxis.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\logAxis.py',
   'DATA'),
  ('pyqtgraph\\examples\\multiplePlotSpeedTest.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\multiplePlotSpeedTest.py',
   'DATA'),
  ('pyqtgraph\\examples\\multiprocess.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\multiprocess.py',
   'DATA'),
  ('pyqtgraph\\examples\\optics\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\optics\\__init__.py',
   'DATA'),
  ('pyqtgraph\\examples\\optics\\pyoptic.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\optics\\pyoptic.py',
   'DATA'),
  ('pyqtgraph\\examples\\optics\\schott_glasses.csv.gz',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\optics\\schott_glasses.csv.gz',
   'DATA'),
  ('pyqtgraph\\examples\\optics_demos.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\optics_demos.py',
   'DATA'),
  ('pyqtgraph\\examples\\parallelize.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\parallelize.py',
   'DATA'),
  ('pyqtgraph\\examples\\parametertree.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\parametertree.py',
   'DATA'),
  ('pyqtgraph\\examples\\py2exe\\plotTest.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\py2exe\\plotTest.py',
   'DATA'),
  ('pyqtgraph\\examples\\py2exe\\setup.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\py2exe\\setup.py',
   'DATA'),
  ('pyqtgraph\\examples\\relativity\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\relativity\\__init__.py',
   'DATA'),
  ('pyqtgraph\\examples\\relativity\\presets\\Grid Expansion.cfg',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\relativity\\presets\\Grid '
   'Expansion.cfg',
   'DATA'),
  ('pyqtgraph\\examples\\relativity\\presets\\Twin Paradox (grid).cfg',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\relativity\\presets\\Twin '
   'Paradox (grid).cfg',
   'DATA'),
  ('pyqtgraph\\examples\\relativity\\presets\\Twin Paradox.cfg',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\relativity\\presets\\Twin '
   'Paradox.cfg',
   'DATA'),
  ('pyqtgraph\\examples\\relativity\\relativity.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\relativity\\relativity.py',
   'DATA'),
  ('pyqtgraph\\examples\\relativity_demo.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\relativity_demo.py',
   'DATA'),
  ('pyqtgraph\\examples\\scrollingPlots.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\scrollingPlots.py',
   'DATA'),
  ('pyqtgraph\\examples\\syntax.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\syntax.py',
   'DATA'),
  ('pyqtgraph\\examples\\template.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\template.py',
   'DATA'),
  ('pyqtgraph\\examples\\test_examples.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\test_examples.py',
   'DATA'),
  ('pyqtgraph\\examples\\text.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\text.py',
   'DATA'),
  ('pyqtgraph\\examples\\utils.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\utils.py',
   'DATA'),
  ('pyqtgraph\\examples\\verlet_chain\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\verlet_chain\\__init__.py',
   'DATA'),
  ('pyqtgraph\\examples\\verlet_chain\\chain.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\verlet_chain\\chain.py',
   'DATA'),
  ('pyqtgraph\\examples\\verlet_chain\\relax.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\verlet_chain\\relax.py',
   'DATA'),
  ('pyqtgraph\\examples\\verlet_chain_demo.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\verlet_chain_demo.py',
   'DATA'),
  ('pyqtgraph\\exceptionHandling.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exceptionHandling.py',
   'DATA'),
  ('pyqtgraph\\exporters\\CSVExporter.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\CSVExporter.py',
   'DATA'),
  ('pyqtgraph\\exporters\\Exporter.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\Exporter.py',
   'DATA'),
  ('pyqtgraph\\exporters\\HDF5Exporter.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\HDF5Exporter.py',
   'DATA'),
  ('pyqtgraph\\exporters\\ImageExporter.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\ImageExporter.py',
   'DATA'),
  ('pyqtgraph\\exporters\\Matplotlib.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\Matplotlib.py',
   'DATA'),
  ('pyqtgraph\\exporters\\PrintExporter.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\PrintExporter.py',
   'DATA'),
  ('pyqtgraph\\exporters\\SVGExporter.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\SVGExporter.py',
   'DATA'),
  ('pyqtgraph\\exporters\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\__init__.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\Flowchart.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\Flowchart.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\FlowchartCtrlTemplate_generic.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\FlowchartCtrlTemplate_generic.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\FlowchartGraphicsView.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\FlowchartGraphicsView.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\Node.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\Node.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\NodeLibrary.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\NodeLibrary.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\Terminal.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\Terminal.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\__init__.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\library\\Data.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\Data.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\library\\Display.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\Display.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\library\\Filters.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\Filters.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\library\\Operators.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\Operators.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\library\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\__init__.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\library\\common.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\common.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\library\\functions.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\functions.py',
   'DATA'),
  ('pyqtgraph\\frozenSupport.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\frozenSupport.py',
   'DATA'),
  ('pyqtgraph\\functions.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\functions.py',
   'DATA'),
  ('pyqtgraph\\functions_numba.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\functions_numba.py',
   'DATA'),
  ('pyqtgraph\\functions_qimage.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\functions_qimage.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ArrowItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ArrowItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\AxisItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\AxisItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\BarGraphItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\BarGraphItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ButtonItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ButtonItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ColorBarItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ColorBarItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\CurvePoint.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\CurvePoint.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\DateAxisItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\DateAxisItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ErrorBarItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ErrorBarItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\FillBetweenItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\FillBetweenItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\GradientEditorItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GradientEditorItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\GradientLegend.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GradientLegend.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\GradientPresets.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GradientPresets.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\GraphItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\GraphicsItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\GraphicsLayout.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsLayout.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\GraphicsObject.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsObject.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\GraphicsWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsWidget.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\GraphicsWidgetAnchor.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsWidgetAnchor.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\GridItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GridItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\HistogramLUTItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\HistogramLUTItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ImageItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ImageItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\InfiniteLine.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\InfiniteLine.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\IsocurveItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\IsocurveItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ItemGroup.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ItemGroup.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\LabelItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\LabelItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\LegendItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\LegendItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\LinearRegionItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\LinearRegionItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\MultiPlotItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\MultiPlotItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\NonUniformImage.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\NonUniformImage.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\PColorMeshItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PColorMeshItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\PlotCurveItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotCurveItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\PlotDataItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotDataItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\PlotItem\\PlotItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotItem\\PlotItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\PlotItem\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotItem\\__init__.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\PlotItem\\plotConfigTemplate_generic.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotItem\\plotConfigTemplate_generic.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ROI.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ROI.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ScaleBar.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ScaleBar.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ScatterPlotItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ScatterPlotItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\TargetItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\TargetItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\TextItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\TextItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\UIGraphicsItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\UIGraphicsItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\VTickGroup.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\VTickGroup.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ViewBox\\ViewBox.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\ViewBox.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ViewBox\\ViewBoxMenu.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\ViewBoxMenu.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ViewBox\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\__init__.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ViewBox\\axisCtrlTemplate_generic.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\axisCtrlTemplate_generic.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\__init__.py',
   'DATA'),
  ('pyqtgraph\\icons\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\__init__.py',
   'DATA'),
  ('pyqtgraph\\icons\\auto.png',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\auto.png',
   'DATA'),
  ('pyqtgraph\\icons\\ctrl.png',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\ctrl.png',
   'DATA'),
  ('pyqtgraph\\icons\\default.png',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\default.png',
   'DATA'),
  ('pyqtgraph\\icons\\icons.svg',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\icons.svg',
   'DATA'),
  ('pyqtgraph\\icons\\invisibleEye.svg',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\invisibleEye.svg',
   'DATA'),
  ('pyqtgraph\\icons\\lock.png',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\lock.png',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\peegee.svg',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\peegee.svg',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\peegee_128px.png',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\peegee_128px.png',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\<EMAIL>',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\<EMAIL>',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\peegee_192px.png',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\peegee_192px.png',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\peegee_256px.png',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\peegee_256px.png',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\<EMAIL>',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\<EMAIL>',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\peegee_512px.png',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\peegee_512px.png',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\<EMAIL>',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\<EMAIL>',
   'DATA'),
  ('pyqtgraph\\imageview\\ImageView.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\imageview\\ImageView.py',
   'DATA'),
  ('pyqtgraph\\imageview\\ImageViewTemplate_generic.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\imageview\\ImageViewTemplate_generic.py',
   'DATA'),
  ('pyqtgraph\\imageview\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\imageview\\__init__.py',
   'DATA'),
  ('pyqtgraph\\jupyter\\GraphicsView.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\jupyter\\GraphicsView.py',
   'DATA'),
  ('pyqtgraph\\jupyter\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\jupyter\\__init__.py',
   'DATA'),
  ('pyqtgraph\\metaarray\\MetaArray.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\metaarray\\MetaArray.py',
   'DATA'),
  ('pyqtgraph\\metaarray\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\metaarray\\__init__.py',
   'DATA'),
  ('pyqtgraph\\multiprocess\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\multiprocess\\__init__.py',
   'DATA'),
  ('pyqtgraph\\multiprocess\\bootstrap.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\multiprocess\\bootstrap.py',
   'DATA'),
  ('pyqtgraph\\multiprocess\\parallelizer.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\multiprocess\\parallelizer.py',
   'DATA'),
  ('pyqtgraph\\multiprocess\\processes.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\multiprocess\\processes.py',
   'DATA'),
  ('pyqtgraph\\multiprocess\\remoteproxy.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\multiprocess\\remoteproxy.py',
   'DATA'),
  ('pyqtgraph\\opengl\\GLGraphicsItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\GLGraphicsItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\GLViewWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\GLViewWidget.py',
   'DATA'),
  ('pyqtgraph\\opengl\\MeshData.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\MeshData.py',
   'DATA'),
  ('pyqtgraph\\opengl\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\__init__.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLAxisItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLAxisItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLBarGraphItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLBarGraphItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLBoxItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLBoxItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLGradientLegendItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLGradientLegendItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLGraphItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLGraphItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLGridItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLGridItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLImageItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLImageItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLLinePlotItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLLinePlotItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLMeshItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLMeshItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLScatterPlotItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLScatterPlotItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLSurfacePlotItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLSurfacePlotItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLTextItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLTextItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLVolumeItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLVolumeItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\__init__.py',
   'DATA'),
  ('pyqtgraph\\opengl\\shaders.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\shaders.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\Parameter.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\Parameter.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\ParameterItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\ParameterItem.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\ParameterSystem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\ParameterSystem.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\ParameterTree.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\ParameterTree.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\SystemSolver.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\SystemSolver.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\__init__.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\interactive.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\interactive.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\__init__.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\action.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\action.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\actiongroup.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\actiongroup.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\basetypes.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\basetypes.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\bool.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\bool.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\calendar.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\calendar.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\checklist.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\checklist.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\color.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\color.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\colormap.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\colormap.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\colormaplut.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\colormaplut.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\file.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\file.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\font.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\font.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\list.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\list.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\numeric.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\numeric.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\pen.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\pen.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\progress.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\progress.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\qtenum.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\qtenum.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\slider.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\slider.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\str.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\str.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\text.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\text.py',
   'DATA'),
  ('pyqtgraph\\reload.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\reload.py',
   'DATA'),
  ('pyqtgraph\\units.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\units.py',
   'DATA'),
  ('pyqtgraph\\util\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\__init__.py',
   'DATA'),
  ('pyqtgraph\\util\\colorama\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\colorama\\__init__.py',
   'DATA'),
  ('pyqtgraph\\util\\colorama\\win32.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\colorama\\win32.py',
   'DATA'),
  ('pyqtgraph\\util\\colorama\\winterm.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\colorama\\winterm.py',
   'DATA'),
  ('pyqtgraph\\util\\cprint.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\cprint.py',
   'DATA'),
  ('pyqtgraph\\util\\cupy_helper.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\cupy_helper.py',
   'DATA'),
  ('pyqtgraph\\util\\garbage_collector.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\garbage_collector.py',
   'DATA'),
  ('pyqtgraph\\util\\get_resolution.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\get_resolution.py',
   'DATA'),
  ('pyqtgraph\\util\\glinfo.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\glinfo.py',
   'DATA'),
  ('pyqtgraph\\util\\mutex.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\mutex.py',
   'DATA'),
  ('pyqtgraph\\util\\numba_helper.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\numba_helper.py',
   'DATA'),
  ('pyqtgraph\\widgets\\BusyCursor.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\BusyCursor.py',
   'DATA'),
  ('pyqtgraph\\widgets\\CheckTable.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\CheckTable.py',
   'DATA'),
  ('pyqtgraph\\widgets\\ColorButton.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ColorButton.py',
   'DATA'),
  ('pyqtgraph\\widgets\\ColorMapButton.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ColorMapButton.py',
   'DATA'),
  ('pyqtgraph\\widgets\\ColorMapMenu.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ColorMapMenu.py',
   'DATA'),
  ('pyqtgraph\\widgets\\ColorMapWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ColorMapWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\ComboBox.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ComboBox.py',
   'DATA'),
  ('pyqtgraph\\widgets\\DataFilterWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\DataFilterWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\DataTreeWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\DataTreeWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\DiffTreeWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\DiffTreeWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\FeedbackButton.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\FeedbackButton.py',
   'DATA'),
  ('pyqtgraph\\widgets\\FileDialog.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\FileDialog.py',
   'DATA'),
  ('pyqtgraph\\widgets\\GradientWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\GradientWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\GraphicsLayoutWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\GraphicsLayoutWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\GraphicsView.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\GraphicsView.py',
   'DATA'),
  ('pyqtgraph\\widgets\\GroupBox.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\GroupBox.py',
   'DATA'),
  ('pyqtgraph\\widgets\\HistogramLUTWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\HistogramLUTWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\JoystickButton.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\JoystickButton.py',
   'DATA'),
  ('pyqtgraph\\widgets\\LayoutWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\LayoutWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\MatplotlibWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\MatplotlibWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\MultiPlotWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\MultiPlotWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\PathButton.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\PathButton.py',
   'DATA'),
  ('pyqtgraph\\widgets\\PenPreviewLabel.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\PenPreviewLabel.py',
   'DATA'),
  ('pyqtgraph\\widgets\\PlotWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\PlotWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\ProgressDialog.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ProgressDialog.py',
   'DATA'),
  ('pyqtgraph\\widgets\\RawImageWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\RawImageWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\RemoteGraphicsView.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\RemoteGraphicsView.py',
   'DATA'),
  ('pyqtgraph\\widgets\\ScatterPlotWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ScatterPlotWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\SpinBox.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\SpinBox.py',
   'DATA'),
  ('pyqtgraph\\widgets\\TableWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\TableWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\TreeWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\TreeWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\ValueLabel.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ValueLabel.py',
   'DATA'),
  ('pyqtgraph\\widgets\\VerticalLabel.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\VerticalLabel.py',
   'DATA'),
  ('pyqtgraph\\widgets\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\__init__.py',
   'DATA')],
 '3.12.9 (tags/v3.12.9:fdb8142, Feb  4 2025, 15:27:58) [MSC v.1942 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt6',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt6.py',
   'PYSOURCE'),
  ('pyi_rth_pyqtgraph_multiprocess',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pyqtgraph_multiprocess.py',
   'PYSOURCE'),
  ('main', 'O:\\python\\shangweiji_test\\main.py', 'PYSOURCE')],
 [('importlib.util',
   'I:\\Program Files\\python\\python12\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib',
   'I:\\Program Files\\python\\python12\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('threading',
   'I:\\Program Files\\python\\python12\\Lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'I:\\Program Files\\python\\python12\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('contextlib',
   'I:\\Program Files\\python\\python12\\Lib\\contextlib.py',
   'PYMODULE'),
  ('_strptime',
   'I:\\Program Files\\python\\python12\\Lib\\_strptime.py',
   'PYMODULE'),
  ('datetime',
   'I:\\Program Files\\python\\python12\\Lib\\datetime.py',
   'PYMODULE'),
  ('_pydatetime',
   'I:\\Program Files\\python\\python12\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('calendar',
   'I:\\Program Files\\python\\python12\\Lib\\calendar.py',
   'PYMODULE'),
  ('argparse',
   'I:\\Program Files\\python\\python12\\Lib\\argparse.py',
   'PYMODULE'),
  ('textwrap',
   'I:\\Program Files\\python\\python12\\Lib\\textwrap.py',
   'PYMODULE'),
  ('shutil', 'I:\\Program Files\\python\\python12\\Lib\\shutil.py', 'PYMODULE'),
  ('zipfile',
   'I:\\Program Files\\python\\python12\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'I:\\Program Files\\python\\python12\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'I:\\Program Files\\python\\python12\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('pathlib',
   'I:\\Program Files\\python\\python12\\Lib\\pathlib.py',
   'PYMODULE'),
  ('urllib.parse',
   'I:\\Program Files\\python\\python12\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib',
   'I:\\Program Files\\python\\python12\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('ipaddress',
   'I:\\Program Files\\python\\python12\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('py_compile',
   'I:\\Program Files\\python\\python12\\Lib\\py_compile.py',
   'PYMODULE'),
  ('importlib.machinery',
   'I:\\Program Files\\python\\python12\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('struct', 'I:\\Program Files\\python\\python12\\Lib\\struct.py', 'PYMODULE'),
  ('tarfile',
   'I:\\Program Files\\python\\python12\\Lib\\tarfile.py',
   'PYMODULE'),
  ('gzip', 'I:\\Program Files\\python\\python12\\Lib\\gzip.py', 'PYMODULE'),
  ('_compression',
   'I:\\Program Files\\python\\python12\\Lib\\_compression.py',
   'PYMODULE'),
  ('lzma', 'I:\\Program Files\\python\\python12\\Lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'I:\\Program Files\\python\\python12\\Lib\\bz2.py', 'PYMODULE'),
  ('fnmatch',
   'I:\\Program Files\\python\\python12\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('copy', 'I:\\Program Files\\python\\python12\\Lib\\copy.py', 'PYMODULE'),
  ('gettext',
   'I:\\Program Files\\python\\python12\\Lib\\gettext.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'I:\\Program Files\\python\\python12\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('typing', 'I:\\Program Files\\python\\python12\\Lib\\typing.py', 'PYMODULE'),
  ('importlib.abc',
   'I:\\Program Files\\python\\python12\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'I:\\Program Files\\python\\python12\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'I:\\Program Files\\python\\python12\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'I:\\Program Files\\python\\python12\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile',
   'I:\\Program Files\\python\\python12\\Lib\\tempfile.py',
   'PYMODULE'),
  ('random', 'I:\\Program Files\\python\\python12\\Lib\\random.py', 'PYMODULE'),
  ('statistics',
   'I:\\Program Files\\python\\python12\\Lib\\statistics.py',
   'PYMODULE'),
  ('decimal',
   'I:\\Program Files\\python\\python12\\Lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'I:\\Program Files\\python\\python12\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'I:\\Program Files\\python\\python12\\Lib\\contextvars.py',
   'PYMODULE'),
  ('fractions',
   'I:\\Program Files\\python\\python12\\Lib\\fractions.py',
   'PYMODULE'),
  ('numbers',
   'I:\\Program Files\\python\\python12\\Lib\\numbers.py',
   'PYMODULE'),
  ('hashlib',
   'I:\\Program Files\\python\\python12\\Lib\\hashlib.py',
   'PYMODULE'),
  ('logging',
   'I:\\Program Files\\python\\python12\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle', 'I:\\Program Files\\python\\python12\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'I:\\Program Files\\python\\python12\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses',
   'I:\\Program Files\\python\\python12\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('_compat_pickle',
   'I:\\Program Files\\python\\python12\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('string', 'I:\\Program Files\\python\\python12\\Lib\\string.py', 'PYMODULE'),
  ('bisect', 'I:\\Program Files\\python\\python12\\Lib\\bisect.py', 'PYMODULE'),
  ('importlib.metadata._itertools',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'I:\\Program Files\\python\\python12\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'I:\\Program Files\\python\\python12\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message',
   'I:\\Program Files\\python\\python12\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'I:\\Program Files\\python\\python12\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'I:\\Program Files\\python\\python12\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'I:\\Program Files\\python\\python12\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'I:\\Program Files\\python\\python12\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'I:\\Program Files\\python\\python12\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.iterators',
   'I:\\Program Files\\python\\python12\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'I:\\Program Files\\python\\python12\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'I:\\Program Files\\python\\python12\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'I:\\Program Files\\python\\python12\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'I:\\Program Files\\python\\python12\\Lib\\getopt.py', 'PYMODULE'),
  ('email.charset',
   'I:\\Program Files\\python\\python12\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'I:\\Program Files\\python\\python12\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'I:\\Program Files\\python\\python12\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'I:\\Program Files\\python\\python12\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header',
   'I:\\Program Files\\python\\python12\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.errors',
   'I:\\Program Files\\python\\python12\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.utils',
   'I:\\Program Files\\python\\python12\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'I:\\Program Files\\python\\python12\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('socket', 'I:\\Program Files\\python\\python12\\Lib\\socket.py', 'PYMODULE'),
  ('selectors',
   'I:\\Program Files\\python\\python12\\Lib\\selectors.py',
   'PYMODULE'),
  ('quopri', 'I:\\Program Files\\python\\python12\\Lib\\quopri.py', 'PYMODULE'),
  ('inspect',
   'I:\\Program Files\\python\\python12\\Lib\\inspect.py',
   'PYMODULE'),
  ('token', 'I:\\Program Files\\python\\python12\\Lib\\token.py', 'PYMODULE'),
  ('dis', 'I:\\Program Files\\python\\python12\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'I:\\Program Files\\python\\python12\\Lib\\opcode.py', 'PYMODULE'),
  ('ast', 'I:\\Program Files\\python\\python12\\Lib\\ast.py', 'PYMODULE'),
  ('email',
   'I:\\Program Files\\python\\python12\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'I:\\Program Files\\python\\python12\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.feedparser',
   'I:\\Program Files\\python\\python12\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('csv', 'I:\\Program Files\\python\\python12\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.readers',
   'I:\\Program Files\\python\\python12\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'I:\\Program Files\\python\\python12\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('tokenize',
   'I:\\Program Files\\python\\python12\\Lib\\tokenize.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'I:\\Program Files\\python\\python12\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._abc',
   'I:\\Program Files\\python\\python12\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('setuptools',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._distutils.errors',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('subprocess',
   'I:\\Program Files\\python\\python12\\Lib\\subprocess.py',
   'PYMODULE'),
  ('signal', 'I:\\Program Files\\python\\python12\\Lib\\signal.py', 'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('unittest.mock',
   'I:\\Program Files\\python\\python12\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest',
   'I:\\Program Files\\python\\python12\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'I:\\Program Files\\python\\python12\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   'I:\\Program Files\\python\\python12\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'I:\\Program Files\\python\\python12\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'I:\\Program Files\\python\\python12\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'I:\\Program Files\\python\\python12\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'I:\\Program Files\\python\\python12\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'I:\\Program Files\\python\\python12\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest._log',
   'I:\\Program Files\\python\\python12\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('difflib',
   'I:\\Program Files\\python\\python12\\Lib\\difflib.py',
   'PYMODULE'),
  ('unittest.result',
   'I:\\Program Files\\python\\python12\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'I:\\Program Files\\python\\python12\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('pkgutil',
   'I:\\Program Files\\python\\python12\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   'I:\\Program Files\\python\\python12\\Lib\\zipimport.py',
   'PYMODULE'),
  ('asyncio',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('ssl', 'I:\\Program Files\\python\\python12\\Lib\\ssl.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   'I:\\Program Files\\python\\python12\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'I:\\Program Files\\python\\python12\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('queue', 'I:\\Program Files\\python\\python12\\Lib\\queue.py', 'PYMODULE'),
  ('concurrent.futures.process',
   'I:\\Program Files\\python\\python12\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'I:\\Program Files\\python\\python12\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'I:\\Program Files\\python\\python12\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'I:\\Program Files\\python\\python12\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('runpy', 'I:\\Program Files\\python\\python12\\Lib\\runpy.py', 'PYMODULE'),
  ('multiprocessing.util',
   'I:\\Program Files\\python\\python12\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'I:\\Program Files\\python\\python12\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'I:\\Program Files\\python\\python12\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'I:\\Program Files\\python\\python12\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'I:\\Program Files\\python\\python12\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'I:\\Program Files\\python\\python12\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('ctypes',
   'I:\\Program Files\\python\\python12\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'I:\\Program Files\\python\\python12\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'I:\\Program Files\\python\\python12\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'I:\\Program Files\\python\\python12\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets',
   'I:\\Program Files\\python\\python12\\Lib\\secrets.py',
   'PYMODULE'),
  ('hmac', 'I:\\Program Files\\python\\python12\\Lib\\hmac.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'I:\\Program Files\\python\\python12\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'I:\\Program '
   'Files\\python\\python12\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'I:\\Program Files\\python\\python12\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'I:\\Program Files\\python\\python12\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'I:\\Program Files\\python\\python12\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'I:\\Program Files\\python\\python12\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'I:\\Program Files\\python\\python12\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass',
   'I:\\Program Files\\python\\python12\\Lib\\getpass.py',
   'PYMODULE'),
  ('nturl2path',
   'I:\\Program Files\\python\\python12\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib', 'I:\\Program Files\\python\\python12\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'I:\\Program Files\\python\\python12\\Lib\\netrc.py', 'PYMODULE'),
  ('mimetypes',
   'I:\\Program Files\\python\\python12\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('http.cookiejar',
   'I:\\Program Files\\python\\python12\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http',
   'I:\\Program Files\\python\\python12\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('urllib.response',
   'I:\\Program Files\\python\\python12\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'I:\\Program Files\\python\\python12\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('xml.sax',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client',
   'I:\\Program Files\\python\\python12\\Lib\\http\\client.py',
   'PYMODULE'),
  ('multiprocessing',
   'I:\\Program Files\\python\\python12\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'I:\\Program Files\\python\\python12\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'I:\\Program Files\\python\\python12\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.constants',
   'I:\\Program Files\\python\\python12\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('sysconfig',
   'I:\\Program Files\\python\\python12\\Lib\\sysconfig.py',
   'PYMODULE'),
  ('_aix_support',
   'I:\\Program Files\\python\\python12\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('platform',
   'I:\\Program Files\\python\\python12\\Lib\\platform.py',
   'PYMODULE'),
  ('json',
   'I:\\Program Files\\python\\python12\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'I:\\Program Files\\python\\python12\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'I:\\Program Files\\python\\python12\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'I:\\Program Files\\python\\python12\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('site', 'I:\\Program Files\\python\\python12\\Lib\\site.py', 'PYMODULE'),
  ('rlcompleter',
   'I:\\Program Files\\python\\python12\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'I:\\Program Files\\python\\python12\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('pydoc', 'I:\\Program Files\\python\\python12\\Lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser',
   'I:\\Program Files\\python\\python12\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('shlex', 'I:\\Program Files\\python\\python12\\Lib\\shlex.py', 'PYMODULE'),
  ('http.server',
   'I:\\Program Files\\python\\python12\\Lib\\http\\server.py',
   'PYMODULE'),
  ('socketserver',
   'I:\\Program Files\\python\\python12\\Lib\\socketserver.py',
   'PYMODULE'),
  ('html',
   'I:\\Program Files\\python\\python12\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'I:\\Program Files\\python\\python12\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'I:\\Program Files\\python\\python12\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'I:\\Program Files\\python\\python12\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty', 'I:\\Program Files\\python\\python12\\Lib\\tty.py', 'PYMODULE'),
  ('setuptools._distutils.core',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('configparser',
   'I:\\Program Files\\python\\python12\\Lib\\configparser.py',
   'PYMODULE'),
  ('packaging.utils',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._elffile',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging.version',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging._structures',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging.tags',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.version',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.extension',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools._path',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.dist',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('backports',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.compat',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('packaging.requirements',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging._parser',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools.installer',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools.errors',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('packaging.licenses',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('tomllib',
   'I:\\Program Files\\python\\python12\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'I:\\Program Files\\python\\python12\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._types',
   'I:\\Program Files\\python\\python12\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tomllib._re',
   'I:\\Program Files\\python\\python12\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools._static',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.markers',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('glob', 'I:\\Program Files\\python\\python12\\Lib\\glob.py', 'PYMODULE'),
  ('setuptools._shutil',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'I:\\Program Files\\python\\python12\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('setuptools.command',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.logging',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('__future__',
   'I:\\Program Files\\python\\python12\\Lib\\__future__.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.VerticalLabel',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\VerticalLabel.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ValueLabel',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ValueLabel.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.TreeWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\TreeWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.TableWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\TableWidget.py',
   'PYMODULE'),
  ('numpy',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.strings',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy._utils',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.char',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy.rec',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy._core.records',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy.typing',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy.f2py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('fileinput',
   'I:\\Program Files\\python\\python12\\Lib\\fileinput.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.matlib',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.testing',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('doctest',
   'I:\\Program Files\\python\\python12\\Lib\\doctest.py',
   'PYMODULE'),
  ('pdb', 'I:\\Program Files\\python\\python12\\Lib\\pdb.py', 'PYMODULE'),
  ('code', 'I:\\Program Files\\python\\python12\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'I:\\Program Files\\python\\python12\\Lib\\codeop.py', 'PYMODULE'),
  ('bdb', 'I:\\Program Files\\python\\python12\\Lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'I:\\Program Files\\python\\python12\\Lib\\cmd.py', 'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\ctypeslib\\__init__.py',
   'PYMODULE'),
  ('numpy.ctypeslib._ctypeslib',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\ctypeslib\\_ctypeslib.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy.ma',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.fft',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.linalg',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._format_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_format_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.__config__',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._globals',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy.version',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.SpinBox',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\SpinBox.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ScatterPlotWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ScatterPlotWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.RemoteGraphicsView',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\RemoteGraphicsView.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.RawImageWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\RawImageWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ProgressDialog',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ProgressDialog.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.PlotWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\PlotWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.PenPreviewLabel',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\PenPreviewLabel.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.PathButton',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\PathButton.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.MultiPlotWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\MultiPlotWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.MatplotlibWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\MatplotlibWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.LayoutWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\LayoutWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.JoystickButton',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\JoystickButton.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.HistogramLUTWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\HistogramLUTWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.GroupBox',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\GroupBox.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.GraphicsView',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\GraphicsView.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.GraphicsLayoutWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\GraphicsLayoutWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.GradientWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\GradientWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.FileDialog',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\FileDialog.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.FeedbackButton',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\FeedbackButton.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.DiffTreeWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\DiffTreeWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.DataTreeWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\DataTreeWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.DataFilterWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\DataFilterWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ComboBox',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ComboBox.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ColorMapWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ColorMapWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ColorMapMenu',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ColorMapMenu.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ColorMapButton',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ColorMapButton.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ColorButton',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ColorButton.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.CheckTable',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\CheckTable.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.BusyCursor',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\BusyCursor.py',
   'PYMODULE'),
  ('pyqtgraph.widgets',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.util.numba_helper',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\numba_helper.py',
   'PYMODULE'),
  ('pyqtgraph.util.mutex',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\mutex.py',
   'PYMODULE'),
  ('pyqtgraph.util.glinfo',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\glinfo.py',
   'PYMODULE'),
  ('pyqtgraph.util.get_resolution',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\get_resolution.py',
   'PYMODULE'),
  ('pyqtgraph.util.garbage_collector',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\garbage_collector.py',
   'PYMODULE'),
  ('pyqtgraph.util.cupy_helper',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\cupy_helper.py',
   'PYMODULE'),
  ('pyqtgraph.util.cprint',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\cprint.py',
   'PYMODULE'),
  ('pyqtgraph.util.colorama.winterm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\colorama\\winterm.py',
   'PYMODULE'),
  ('pyqtgraph.util.colorama.win32',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\colorama\\win32.py',
   'PYMODULE'),
  ('pyqtgraph.util.colorama',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\colorama\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.util',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.units',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\units.py',
   'PYMODULE'),
  ('pyqtgraph.reload',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\reload.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.text',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\text.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.str',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\str.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.slider',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\slider.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.qtenum',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\qtenum.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.progress',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\progress.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.pen',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\pen.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.numeric',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\numeric.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.list',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\list.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.font',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\font.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.file',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\file.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.colormaplut',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\colormaplut.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.colormap',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\colormap.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.color',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\color.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.checklist',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\checklist.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.calendar',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\calendar.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.bool',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\bool.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.basetypes',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\basetypes.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.actiongroup',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\actiongroup.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.action',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\action.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.interactive',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\interactive.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.SystemSolver',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\SystemSolver.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.ParameterTree',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\ParameterTree.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.ParameterSystem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\ParameterSystem.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.ParameterItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\ParameterItem.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.Parameter',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\Parameter.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.multiprocess.remoteproxy',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\multiprocess\\remoteproxy.py',
   'PYMODULE'),
  ('pyqtgraph.multiprocess.processes',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\multiprocess\\processes.py',
   'PYMODULE'),
  ('pyqtgraph.multiprocess.parallelizer',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\multiprocess\\parallelizer.py',
   'PYMODULE'),
  ('pyqtgraph.multiprocess.bootstrap',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\multiprocess\\bootstrap.py',
   'PYMODULE'),
  ('pyqtgraph.multiprocess',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\multiprocess\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.metaarray.MetaArray',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\metaarray\\MetaArray.py',
   'PYMODULE'),
  ('pyqtgraph.metaarray',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\metaarray\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.imageview.ImageViewTemplate_generic',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\imageview\\ImageViewTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.imageview.ImageView',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\imageview\\ImageView.py',
   'PYMODULE'),
  ('pyqtgraph.imageview',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\imageview\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.icons',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ViewBox.axisCtrlTemplate_generic',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\axisCtrlTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ViewBox.ViewBoxMenu',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\ViewBoxMenu.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ViewBox.ViewBox',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\ViewBox.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ViewBox',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.VTickGroup',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\VTickGroup.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.UIGraphicsItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\UIGraphicsItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.TextItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\TextItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.TargetItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\TargetItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ScatterPlotItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ScatterPlotItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ScaleBar',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ScaleBar.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ROI',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ROI.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.PlotItem.plotConfigTemplate_generic',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotItem\\plotConfigTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.PlotItem.PlotItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotItem\\PlotItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.PlotItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotItem\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.PlotDataItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotDataItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.PlotCurveItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotCurveItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.PColorMeshItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PColorMeshItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.NonUniformImage',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\NonUniformImage.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.MultiPlotItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\MultiPlotItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.LinearRegionItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\LinearRegionItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.LegendItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\LegendItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.LabelItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\LabelItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ItemGroup',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ItemGroup.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.IsocurveItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\IsocurveItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.InfiniteLine',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\InfiniteLine.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ImageItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ImageItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.HistogramLUTItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\HistogramLUTItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GridItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GridItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GraphicsWidgetAnchor',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsWidgetAnchor.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GraphicsWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsWidget.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GraphicsObject',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsObject.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GraphicsLayout',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsLayout.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GraphicsItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsItem.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GraphItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GradientPresets',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GradientPresets.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GradientLegend',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GradientLegend.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GradientEditorItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GradientEditorItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.FillBetweenItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\FillBetweenItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ErrorBarItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ErrorBarItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.DateAxisItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\DateAxisItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.CurvePoint',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\CurvePoint.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ColorBarItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ColorBarItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ButtonItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ButtonItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.BarGraphItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\BarGraphItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.AxisItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\AxisItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ArrowItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ArrowItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.functions_qimage',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\functions_qimage.py',
   'PYMODULE'),
  ('pyqtgraph.functions_numba',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\functions_numba.py',
   'PYMODULE'),
  ('pyqtgraph.functions',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\functions.py',
   'PYMODULE'),
  ('pyqtgraph.frozenSupport',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\frozenSupport.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library.functions',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\functions.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library.common',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\common.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library.Operators',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\Operators.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library.Filters',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\Filters.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library.Display',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\Display.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library.Data',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\Data.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.Terminal',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\Terminal.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.NodeLibrary',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\NodeLibrary.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.Node',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\Node.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.FlowchartGraphicsView',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\FlowchartGraphicsView.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.FlowchartCtrlTemplate_generic',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\FlowchartCtrlTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.Flowchart',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\Flowchart.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.SVGExporter',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\SVGExporter.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom',
   'I:\\Program Files\\python\\python12\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.PrintExporter',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\PrintExporter.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.Matplotlib',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\Matplotlib.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.ImageExporter',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\ImageExporter.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.HDF5Exporter',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\HDF5Exporter.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.Exporter',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\Exporter.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.CSVExporter',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\CSVExporter.py',
   'PYMODULE'),
  ('pyqtgraph.exporters',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.exceptionHandling',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exceptionHandling.py',
   'PYMODULE'),
  ('pyqtgraph.examples.verlet_chain_demo',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\verlet_chain_demo.py',
   'PYMODULE'),
  ('pyqtgraph.examples.verlet_chain.relax',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\verlet_chain\\relax.py',
   'PYMODULE'),
  ('pyqtgraph.examples.verlet_chain.chain',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\verlet_chain\\chain.py',
   'PYMODULE'),
  ('pyqtgraph.examples.verlet_chain',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\verlet_chain\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.examples.utils',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\utils.py',
   'PYMODULE'),
  ('pyqtgraph.examples.text',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\text.py',
   'PYMODULE'),
  ('pyqtgraph.examples.test_examples',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\test_examples.py',
   'PYMODULE'),
  ('pyqtgraph.examples.template',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\template.py',
   'PYMODULE'),
  ('pyqtgraph.examples.syntax',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\syntax.py',
   'PYMODULE'),
  ('pyqtgraph.examples.scrollingPlots',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\scrollingPlots.py',
   'PYMODULE'),
  ('pyqtgraph.examples.relativity_demo',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\relativity_demo.py',
   'PYMODULE'),
  ('pyqtgraph.examples.relativity.relativity',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\relativity\\relativity.py',
   'PYMODULE'),
  ('pyqtgraph.examples.relativity',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\relativity\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.examples.parametertree',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\parametertree.py',
   'PYMODULE'),
  ('pyqtgraph.examples.parallelize',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\parallelize.py',
   'PYMODULE'),
  ('pyqtgraph.examples.optics_demos',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\optics_demos.py',
   'PYMODULE'),
  ('pyqtgraph.examples.optics.pyoptic',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\optics\\pyoptic.py',
   'PYMODULE'),
  ('pyqtgraph.examples.optics',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\optics\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.examples.multiprocess',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\multiprocess.py',
   'PYMODULE'),
  ('pyqtgraph.examples.multiplePlotSpeedTest',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\multiplePlotSpeedTest.py',
   'PYMODULE'),
  ('pyqtgraph.examples.logAxis',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\logAxis.py',
   'PYMODULE'),
  ('pyqtgraph.examples.linkedViews',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\linkedViews.py',
   'PYMODULE'),
  ('pyqtgraph.examples.jupyter_console_example',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\jupyter_console_example.py',
   'PYMODULE'),
  ('pyqtgraph.examples.isocurve',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\isocurve.py',
   'PYMODULE'),
  ('pyqtgraph.examples.infiniteline_performance',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\infiniteline_performance.py',
   'PYMODULE'),
  ('pyqtgraph.examples.imageAnalysis',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\imageAnalysis.py',
   'PYMODULE'),
  ('pyqtgraph.examples.histogram',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\histogram.py',
   'PYMODULE'),
  ('pyqtgraph.examples.hdf5',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\hdf5.py',
   'PYMODULE'),
  ('pyqtgraph.examples.glow',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\glow.py',
   'PYMODULE'),
  ('pyqtgraph.examples.fractal',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\fractal.py',
   'PYMODULE'),
  ('pyqtgraph.examples.exampleLoaderTemplate_generic',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\exampleLoaderTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.examples.dockarea',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\dockarea.py',
   'PYMODULE'),
  ('pyqtgraph.examples.designerExample',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\designerExample.py',
   'PYMODULE'),
  ('pyqtgraph.examples.customPlot',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\customPlot.py',
   'PYMODULE'),
  ('pyqtgraph.examples.customGraphicsItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\customGraphicsItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.crosshair',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\crosshair.py',
   'PYMODULE'),
  ('pyqtgraph.examples.contextMenu',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\contextMenu.py',
   'PYMODULE'),
  ('pyqtgraph.examples.console_exception_inspection',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\console_exception_inspection.py',
   'PYMODULE'),
  ('pyqtgraph.examples.colorMapsLinearized',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\colorMapsLinearized.py',
   'PYMODULE'),
  ('pyqtgraph.examples.colorMaps',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\colorMaps.py',
   'PYMODULE'),
  ('pyqtgraph.examples.beeswarm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\beeswarm.py',
   'PYMODULE'),
  ('pyqtgraph.examples._paramtreecfg',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\_paramtreecfg.py',
   'PYMODULE'),
  ('pyqtgraph.examples._buildParamTypes',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\_buildParamTypes.py',
   'PYMODULE'),
  ('pyqtgraph.examples.__main__',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\__main__.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ViewLimits',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ViewLimits.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ViewBoxFeatures',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ViewBoxFeatures.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ViewBox',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ViewBox.py',
   'PYMODULE'),
  ('pyqtgraph.examples.VideoTemplate_generic',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\VideoTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.examples.VideoSpeedTest',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\VideoSpeedTest.py',
   'PYMODULE'),
  ('pyqtgraph.examples.TreeWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\TreeWidget.py',
   'PYMODULE'),
  ('pyqtgraph.examples.TableWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\TableWidget.py',
   'PYMODULE'),
  ('pyqtgraph.examples.Symbols',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\Symbols.py',
   'PYMODULE'),
  ('pyqtgraph.examples.SpinBox',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\SpinBox.py',
   'PYMODULE'),
  ('pyqtgraph.examples.SimplePlot',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\SimplePlot.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ScatterPlotWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ScatterPlotWidget.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ScatterPlotSpeedTest',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ScatterPlotSpeedTest.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ScatterPlot',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ScatterPlot.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ScaleBar',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ScaleBar.py',
   'PYMODULE'),
  ('pyqtgraph.examples.RunExampleApp',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\RunExampleApp.py',
   'PYMODULE'),
  ('pyqtgraph.examples.RemoteSpeedTest',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\RemoteSpeedTest.py',
   'PYMODULE'),
  ('pyqtgraph.examples.RemoteGraphicsView',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\RemoteGraphicsView.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ROItypes',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ROItypes.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ROIExamples',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ROIExamples.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ProgressDialog',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ProgressDialog.py',
   'PYMODULE'),
  ('pyqtgraph.examples.Plotting',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\Plotting.py',
   'PYMODULE'),
  ('pyqtgraph.examples.PlotWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\PlotWidget.py',
   'PYMODULE'),
  ('pyqtgraph.examples.PlotSpeedTest',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\PlotSpeedTest.py',
   'PYMODULE'),
  ('pyqtgraph.examples.PlotAutoRange',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\PlotAutoRange.py',
   'PYMODULE'),
  ('pyqtgraph.examples.PanningPlot',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\PanningPlot.py',
   'PYMODULE'),
  ('pyqtgraph.examples.PColorMeshItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\PColorMeshItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.NonUniformImage',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\NonUniformImage.py',
   'PYMODULE'),
  ('pyqtgraph.examples.MultiplePlotAxes',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\MultiplePlotAxes.py',
   'PYMODULE'),
  ('pyqtgraph.examples.MultiPlotSpeedTest',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\MultiPlotSpeedTest.py',
   'PYMODULE'),
  ('pyqtgraph.examples.MultiDataPlot',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\MultiDataPlot.py',
   'PYMODULE'),
  ('pyqtgraph.examples.MouseSelection',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\MouseSelection.py',
   'PYMODULE'),
  ('pyqtgraph.examples.MatrixDisplayExample',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\MatrixDisplayExample.py',
   'PYMODULE'),
  ('pyqtgraph.examples.LogPlotTest',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\LogPlotTest.py',
   'PYMODULE'),
  ('pyqtgraph.examples.Legend',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\Legend.py',
   'PYMODULE'),
  ('pyqtgraph.examples.JoystickButton',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\JoystickButton.py',
   'PYMODULE'),
  ('pyqtgraph.examples.InteractiveParameter',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\InteractiveParameter.py',
   'PYMODULE'),
  ('pyqtgraph.examples.InfiniteLine',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\InfiniteLine.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ImageView',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ImageView.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ImageItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ImageItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.HistogramLUT',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\HistogramLUT.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GraphicsScene',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GraphicsScene.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GraphicsLayout',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GraphicsLayout.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GraphItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GraphItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GradientWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GradientWidget.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GradientEditor',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GradientEditor.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GLshaders',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLshaders.py',
   'PYMODULE'),
  ('pyqtgraph.opengl',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.MeshData',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\MeshData.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.items.GLVolumeItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLVolumeItem.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.items',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.GLGraphicsItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\GLGraphicsItem.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.items.GLTextItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLTextItem.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.items.GLSurfacePlotItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLSurfacePlotItem.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.items.GLScatterPlotItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLScatterPlotItem.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.items.GLMeshItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLMeshItem.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.items.GLLinePlotItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLLinePlotItem.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.items.GLImageItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLImageItem.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.items.GLGridItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLGridItem.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.items.GLGraphItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLGraphItem.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.items.GLGradientLegendItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLGradientLegendItem.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.items.GLBoxItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLBoxItem.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.items.GLBarGraphItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLBarGraphItem.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.items.GLAxisItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLAxisItem.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.GLViewWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\GLViewWidget.py',
   'PYMODULE'),
  ('pyqtgraph.opengl.shaders',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\shaders.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GLVolumeItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLVolumeItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GLViewWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLViewWidget.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GLTextItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLTextItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GLSurfacePlot',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLSurfacePlot.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GLScatterPlotItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLScatterPlotItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GLPainterItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLPainterItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GLMeshItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLMeshItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GLLinePlotItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLLinePlotItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GLIsosurface',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLIsosurface.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GLImageItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLImageItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GLGraphItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLGraphItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GLGradientLegendItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLGradientLegendItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.GLBarGraphItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLBarGraphItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.FlowchartCustomNode',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\FlowchartCustomNode.py',
   'PYMODULE'),
  ('pyqtgraph.examples.Flowchart',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\Flowchart.py',
   'PYMODULE'),
  ('pyqtgraph.examples.FillBetweenItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\FillBetweenItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ExampleApp',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ExampleApp.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ErrorBarItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ErrorBarItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.Draw',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\Draw.py',
   'PYMODULE'),
  ('pyqtgraph.examples.DiffTreeWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\DiffTreeWidget.py',
   'PYMODULE'),
  ('pyqtgraph.examples.DateAxisItem_QtDesigner',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\DateAxisItem_QtDesigner.py',
   'PYMODULE'),
  ('pyqtgraph.examples.DateAxisItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\DateAxisItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.DataTreeWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\DataTreeWidget.py',
   'PYMODULE'),
  ('pyqtgraph.examples.DataSlicing',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\DataSlicing.py',
   'PYMODULE'),
  ('pyqtgraph.examples.CustomGraphItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\CustomGraphItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ConsoleWidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ConsoleWidget.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ColorGradientPlots',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ColorGradientPlots.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ColorButton',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ColorButton.py',
   'PYMODULE'),
  ('pyqtgraph.examples.ColorBarItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ColorBarItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.CLIexample',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\CLIexample.py',
   'PYMODULE'),
  ('pyqtgraph.examples.BarGraphItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\BarGraphItem.py',
   'PYMODULE'),
  ('pyqtgraph.examples.AxisItem_label_overlap',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\AxisItem_label_overlap.py',
   'PYMODULE'),
  ('pyqtgraph.examples.Arrow',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\Arrow.py',
   'PYMODULE'),
  ('pyqtgraph.examples',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.dockarea.DockDrop',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\dockarea\\DockDrop.py',
   'PYMODULE'),
  ('pyqtgraph.dockarea.DockArea',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\dockarea\\DockArea.py',
   'PYMODULE'),
  ('pyqtgraph.dockarea.Dock',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\dockarea\\Dock.py',
   'PYMODULE'),
  ('pyqtgraph.dockarea.Container',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\dockarea\\Container.py',
   'PYMODULE'),
  ('pyqtgraph.dockarea',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\dockarea\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.debug',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\debug.py',
   'PYMODULE'),
  ('pstats', 'I:\\Program Files\\python\\python12\\Lib\\pstats.py', 'PYMODULE'),
  ('cProfile',
   'I:\\Program Files\\python\\python12\\Lib\\cProfile.py',
   'PYMODULE'),
  ('optparse',
   'I:\\Program Files\\python\\python12\\Lib\\optparse.py',
   'PYMODULE'),
  ('profile',
   'I:\\Program Files\\python\\python12\\Lib\\profile.py',
   'PYMODULE'),
  ('pyqtgraph.console.stackwidget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\stackwidget.py',
   'PYMODULE'),
  ('pyqtgraph.console.repl_widget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\repl_widget.py',
   'PYMODULE'),
  ('pyqtgraph.console.exception_widget',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\exception_widget.py',
   'PYMODULE'),
  ('pyqtgraph.console.Console',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\Console.py',
   'PYMODULE'),
  ('pyqtgraph.console.CmdInput',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\CmdInput.py',
   'PYMODULE'),
  ('pyqtgraph.console',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.configfile',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\configfile.py',
   'PYMODULE'),
  ('pyqtgraph.colors.palette',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\palette.py',
   'PYMODULE'),
  ('pyqtgraph.colors',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.colormap',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colormap.py',
   'PYMODULE'),
  ('pyqtgraph.canvas.TransformGuiTemplate_generic',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\TransformGuiTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.canvas.CanvasTemplate_generic',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\CanvasTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.canvas.CanvasManager',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\CanvasManager.py',
   'PYMODULE'),
  ('pyqtgraph.canvas.CanvasItem',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\CanvasItem.py',
   'PYMODULE'),
  ('pyqtgraph.canvas.Canvas',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\Canvas.py',
   'PYMODULE'),
  ('pyqtgraph.canvas',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.WidgetGroup',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\WidgetGroup.py',
   'PYMODULE'),
  ('pyqtgraph.Vector',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Vector.py',
   'PYMODULE'),
  ('pyqtgraph.Transform3D',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Transform3D.py',
   'PYMODULE'),
  ('pyqtgraph.ThreadsafeTimer',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\ThreadsafeTimer.py',
   'PYMODULE'),
  ('pyqtgraph.SignalProxy',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\SignalProxy.py',
   'PYMODULE'),
  ('pyqtgraph.SRTTransform3D',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\SRTTransform3D.py',
   'PYMODULE'),
  ('pyqtgraph.SRTTransform',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\SRTTransform.py',
   'PYMODULE'),
  ('pyqtgraph.Qt.internals',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\internals.py',
   'PYMODULE'),
  ('pyqtgraph.Qt.compat',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\compat\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.Qt.QtWidgets',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtWidgets\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.Qt.QtGui',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtGui\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.Qt.QtCore',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtCore\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.Qt',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\__init__.py',
   'PYMODULE'),
  ('PyQt6.uic',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\__init__.py',
   'PYMODULE'),
  ('PyQt6.uic.ui_file',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\ui_file.py',
   'PYMODULE'),
  ('PyQt6.uic.exceptions',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\exceptions.py',
   'PYMODULE'),
  ('PyQt6.uic.objcreator',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\objcreator.py',
   'PYMODULE'),
  ('PyQt6.uic.load_ui',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\load_ui.py',
   'PYMODULE'),
  ('PyQt6.uic.Loader.loader',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Loader\\loader.py',
   'PYMODULE'),
  ('PyQt6.uic.Loader',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Loader\\__init__.py',
   'PYMODULE'),
  ('PyQt6.uic.Loader.qobjectcreator',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Loader\\qobjectcreator.py',
   'PYMODULE'),
  ('PyQt6.uic.uiparser',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\uiparser.py',
   'PYMODULE'),
  ('PyQt6.uic.properties',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\properties.py',
   'PYMODULE'),
  ('PyQt6.uic.icon_cache',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\icon_cache.py',
   'PYMODULE'),
  ('PyQt6.uic.enum_map',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\enum_map.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.compiler',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\compiler.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.qobjectcreator',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\qobjectcreator.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.as_string',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\as_string.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.indenter',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\indenter.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.qtproxies',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\qtproxies.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.proxy_metaclass',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\proxy_metaclass.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.misc',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\misc.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\__init__.py',
   'PYMODULE'),
  ('PyQt6.uic.compile_ui',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\compile_ui.py',
   'PYMODULE'),
  ('PyQt6',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.Point',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Point.py',
   'PYMODULE'),
  ('pyqtgraph.PlotData',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\PlotData.py',
   'PYMODULE'),
  ('pyqtgraph.GraphicsScene.mouseEvents',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\mouseEvents.py',
   'PYMODULE'),
  ('pyqtgraph.GraphicsScene.exportDialogTemplate_generic',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\exportDialogTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.GraphicsScene.exportDialog',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\exportDialog.py',
   'PYMODULE'),
  ('pyqtgraph.GraphicsScene.GraphicsScene',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\GraphicsScene.py',
   'PYMODULE'),
  ('pyqtgraph.GraphicsScene',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\__init__.py',
   'PYMODULE'),
  ('tracemalloc',
   'I:\\Program Files\\python\\python12\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('stringprep',
   'I:\\Program Files\\python\\python12\\Lib\\stringprep.py',
   'PYMODULE'),
  ('_py_abc',
   'I:\\Program Files\\python\\python12\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('app.main_window',
   'O:\\python\\shangweiji_test\\app\\main_window.py',
   'PYMODULE'),
  ('app.protocol', 'O:\\python\\shangweiji_test\\app\\protocol.py', 'PYMODULE'),
  ('app', 'O:\\python\\shangweiji_test\\app\\__init__.py', 'PYMODULE'),
  ('app.mock_serial_thread',
   'O:\\python\\shangweiji_test\\app\\mock_serial_thread.py',
   'PYMODULE'),
  ('app.serial_thread',
   'O:\\python\\shangweiji_test\\app\\serial_thread.py',
   'PYMODULE'),
  ('app.crc', 'O:\\python\\shangweiji_test\\app\\crc.py', 'PYMODULE'),
  ('crc16',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\crc16\\__init__.py',
   'PYMODULE'),
  ('crc16.crc16pure',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\crc16\\crc16pure.py',
   'PYMODULE'),
  ('serial.tools.list_ports',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\serial\\tools\\list_ports.py',
   'PYMODULE'),
  ('serial.tools',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\serial\\tools\\__init__.py',
   'PYMODULE'),
  ('serial.tools.list_ports_common',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\serial\\tools\\list_ports_common.py',
   'PYMODULE'),
  ('serial.tools.list_ports_posix',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\serial\\tools\\list_ports_posix.py',
   'PYMODULE'),
  ('serial.tools.list_ports_osx',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\serial\\tools\\list_ports_osx.py',
   'PYMODULE'),
  ('serial.tools.list_ports_linux',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\serial\\tools\\list_ports_linux.py',
   'PYMODULE'),
  ('serial.tools.list_ports_windows',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\serial\\tools\\list_ports_windows.py',
   'PYMODULE'),
  ('serial.win32',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\serial\\win32.py',
   'PYMODULE'),
  ('serial',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\serial\\__init__.py',
   'PYMODULE'),
  ('serial.serialjava',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\serial\\serialjava.py',
   'PYMODULE'),
  ('serial.serialposix',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\serial\\serialposix.py',
   'PYMODULE'),
  ('serial.serialwin32',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\serial\\serialwin32.py',
   'PYMODULE'),
  ('serial.serialcli',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\serial\\serialcli.py',
   'PYMODULE'),
  ('serial.serialutil',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\serial\\serialutil.py',
   'PYMODULE'),
  ('app.settings_dialog',
   'O:\\python\\shangweiji_test\\app\\settings_dialog.py',
   'PYMODULE'),
  ('app.styles', 'O:\\python\\shangweiji_test\\app\\styles.py', 'PYMODULE')],
 [('python312.dll',
   'I:\\Program Files\\python\\python12\\python312.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'BINARY'),
  ('unicodedata.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('select.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp312-win_amd64.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\random\\_philox.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp312-win_amd64.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\random\\_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp312-win_amd64.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\random\\_common.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'I:\\Program Files\\python\\python12\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('PyQt6\\QtTest.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\QtTest.pyd',
   'EXTENSION'),
  ('PyQt6\\QtOpenGLWidgets.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\QtOpenGLWidgets.pyd',
   'EXTENSION'),
  ('PyQt6\\QtOpenGL.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\QtOpenGL.pyd',
   'EXTENSION'),
  ('PyQt6\\QtSvg.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\QtSvg.pyd',
   'EXTENSION'),
  ('PyQt6\\sip.cp312-win_amd64.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\sip.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt6\\QtGui.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt6\\QtCore.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\QtCore.pyd',
   'EXTENSION'),
  ('crc16\\_crc16.cp312-win_amd64.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\crc16\\_crc16.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt6\\QtWidgets.pyd',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\QtWidgets.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'I:\\Program Files\\python\\python12\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'I:\\Program Files\\python\\python12\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'I:\\Program Files\\python\\python12\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'I:\\Program Files\\python\\python12\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'I:\\Program Files\\python\\python12\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('python3.dll', 'I:\\Program Files\\python\\python12\\python3.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Test.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Test.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6OpenGLWidgets.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6OpenGLWidgets.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6OpenGL.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6OpenGL.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'BINARY'),
  ('ucrtbase.dll', 'F:\\ProgramData\\miniconda3\\ucrtbase.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'F:\\ProgramData\\miniconda3\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('pyqtgraph-0.13.7.dist-info\\INSTALLER',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph-0.13.7.dist-info\\INSTALLER',
   'DATA'),
  ('pyqtgraph-0.13.7.dist-info\\LICENSE.txt',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph-0.13.7.dist-info\\LICENSE.txt',
   'DATA'),
  ('pyqtgraph-0.13.7.dist-info\\METADATA',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph-0.13.7.dist-info\\METADATA',
   'DATA'),
  ('pyqtgraph-0.13.7.dist-info\\RECORD',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph-0.13.7.dist-info\\RECORD',
   'DATA'),
  ('pyqtgraph-0.13.7.dist-info\\REQUESTED',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph-0.13.7.dist-info\\REQUESTED',
   'DATA'),
  ('pyqtgraph-0.13.7.dist-info\\WHEEL',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph-0.13.7.dist-info\\WHEEL',
   'DATA'),
  ('pyqtgraph-0.13.7.dist-info\\top_level.txt',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph-0.13.7.dist-info\\top_level.txt',
   'DATA'),
  ('pyqtgraph\\GraphicsScene\\GraphicsScene.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\GraphicsScene.py',
   'DATA'),
  ('pyqtgraph\\GraphicsScene\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\__init__.py',
   'DATA'),
  ('pyqtgraph\\GraphicsScene\\exportDialog.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\exportDialog.py',
   'DATA'),
  ('pyqtgraph\\GraphicsScene\\exportDialogTemplate_generic.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\exportDialogTemplate_generic.py',
   'DATA'),
  ('pyqtgraph\\GraphicsScene\\mouseEvents.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\mouseEvents.py',
   'DATA'),
  ('pyqtgraph\\PlotData.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\PlotData.py',
   'DATA'),
  ('pyqtgraph\\Point.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Point.py',
   'DATA'),
  ('pyqtgraph\\Qt\\QtCore\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtCore\\__init__.py',
   'DATA'),
  ('pyqtgraph\\Qt\\QtCore\\__init__.pyi',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtCore\\__init__.pyi',
   'DATA'),
  ('pyqtgraph\\Qt\\QtGui\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtGui\\__init__.py',
   'DATA'),
  ('pyqtgraph\\Qt\\QtGui\\__init__.pyi',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtGui\\__init__.pyi',
   'DATA'),
  ('pyqtgraph\\Qt\\QtSvg.pyi',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtSvg.pyi',
   'DATA'),
  ('pyqtgraph\\Qt\\QtTest.pyi',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtTest.pyi',
   'DATA'),
  ('pyqtgraph\\Qt\\QtWidgets\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtWidgets\\__init__.py',
   'DATA'),
  ('pyqtgraph\\Qt\\QtWidgets\\__init__.pyi',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtWidgets\\__init__.pyi',
   'DATA'),
  ('pyqtgraph\\Qt\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\__init__.py',
   'DATA'),
  ('pyqtgraph\\Qt\\__init__.pyi',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\__init__.pyi',
   'DATA'),
  ('pyqtgraph\\Qt\\compat\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\compat\\__init__.py',
   'DATA'),
  ('pyqtgraph\\Qt\\internals.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\internals.py',
   'DATA'),
  ('pyqtgraph\\SRTTransform.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\SRTTransform.py',
   'DATA'),
  ('pyqtgraph\\SRTTransform3D.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\SRTTransform3D.py',
   'DATA'),
  ('pyqtgraph\\SignalProxy.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\SignalProxy.py',
   'DATA'),
  ('pyqtgraph\\ThreadsafeTimer.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\ThreadsafeTimer.py',
   'DATA'),
  ('pyqtgraph\\Transform3D.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Transform3D.py',
   'DATA'),
  ('pyqtgraph\\Vector.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\Vector.py',
   'DATA'),
  ('pyqtgraph\\WidgetGroup.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\WidgetGroup.py',
   'DATA'),
  ('pyqtgraph\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\__init__.py',
   'DATA'),
  ('pyqtgraph\\canvas\\Canvas.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\Canvas.py',
   'DATA'),
  ('pyqtgraph\\canvas\\CanvasItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\CanvasItem.py',
   'DATA'),
  ('pyqtgraph\\canvas\\CanvasManager.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\CanvasManager.py',
   'DATA'),
  ('pyqtgraph\\canvas\\CanvasTemplate_generic.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\CanvasTemplate_generic.py',
   'DATA'),
  ('pyqtgraph\\canvas\\TransformGuiTemplate_generic.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\TransformGuiTemplate_generic.py',
   'DATA'),
  ('pyqtgraph\\canvas\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\__init__.py',
   'DATA'),
  ('pyqtgraph\\colormap.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colormap.py',
   'DATA'),
  ('pyqtgraph\\colors\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\__init__.py',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CC-BY license - applies to CET color map data.txt',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CC-BY '
   'license - applies to CET color map data.txt',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CC0 legal code - applies to virids, magma, '
   'plasma, inferno and cividis.txt',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CC0 '
   'legal code - applies to virids, magma, plasma, inferno and cividis.txt',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C1s.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C1s.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C2.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C2s.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C2s.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C3.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C3.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C3s.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C3s.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C4.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C4.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C4s.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C4s.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C5.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C5.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C5s.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C5s.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C6.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C6.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C6s.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C6s.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C7.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C7.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C7s.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C7s.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBC1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBC1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBC2.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBC2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBD1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBD1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBL1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBL1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBL2.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBL2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBTC1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBTC1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBTC2.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBTC2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBTD1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBTD1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBTL1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBTL1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBTL2.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBTL2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D10.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D10.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D11.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D11.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D12.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D12.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D13.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D13.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D1A.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D1A.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D2.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D3.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D3.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D4.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D4.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D6.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D6.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D7.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D7.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D8.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D8.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D9.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D9.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-I1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-I1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-I2.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-I2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-I3.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-I3.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L10.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L10.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L11.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L11.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L12.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L12.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L13.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L13.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L14.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L14.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L15.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L15.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L16.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L16.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L17.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L17.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L18.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L18.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L19.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L19.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L2.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L3.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L3.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L4.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L4.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L5.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L5.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L6.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L6.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L7.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L7.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L8.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L8.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L9.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L9.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-R1.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-R1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-R2.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-R2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-R3.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-R3.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-R4.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-R4.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\PAL-relaxed.hex',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\PAL-relaxed.hex',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\PAL-relaxed_bright.hex',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\PAL-relaxed_bright.hex',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\cividis.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\cividis.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\inferno.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\inferno.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\magma.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\magma.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\plasma.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\plasma.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\turbo.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\turbo.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\viridis.csv',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\viridis.csv',
   'DATA'),
  ('pyqtgraph\\colors\\palette.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\palette.py',
   'DATA'),
  ('pyqtgraph\\configfile.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\configfile.py',
   'DATA'),
  ('pyqtgraph\\console\\CmdInput.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\CmdInput.py',
   'DATA'),
  ('pyqtgraph\\console\\Console.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\Console.py',
   'DATA'),
  ('pyqtgraph\\console\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\__init__.py',
   'DATA'),
  ('pyqtgraph\\console\\exception_widget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\exception_widget.py',
   'DATA'),
  ('pyqtgraph\\console\\repl_widget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\repl_widget.py',
   'DATA'),
  ('pyqtgraph\\console\\stackwidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\stackwidget.py',
   'DATA'),
  ('pyqtgraph\\debug.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\debug.py',
   'DATA'),
  ('pyqtgraph\\dockarea\\Container.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\dockarea\\Container.py',
   'DATA'),
  ('pyqtgraph\\dockarea\\Dock.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\dockarea\\Dock.py',
   'DATA'),
  ('pyqtgraph\\dockarea\\DockArea.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\dockarea\\DockArea.py',
   'DATA'),
  ('pyqtgraph\\dockarea\\DockDrop.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\dockarea\\DockDrop.py',
   'DATA'),
  ('pyqtgraph\\dockarea\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\dockarea\\__init__.py',
   'DATA'),
  ('pyqtgraph\\examples\\Arrow.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\Arrow.py',
   'DATA'),
  ('pyqtgraph\\examples\\AxisItem_label_overlap.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\AxisItem_label_overlap.py',
   'DATA'),
  ('pyqtgraph\\examples\\BarGraphItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\BarGraphItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\CLIexample.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\CLIexample.py',
   'DATA'),
  ('pyqtgraph\\examples\\ColorBarItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ColorBarItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\ColorButton.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ColorButton.py',
   'DATA'),
  ('pyqtgraph\\examples\\ColorGradientPlots.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ColorGradientPlots.py',
   'DATA'),
  ('pyqtgraph\\examples\\ConsoleWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ConsoleWidget.py',
   'DATA'),
  ('pyqtgraph\\examples\\CustomGraphItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\CustomGraphItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\DataSlicing.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\DataSlicing.py',
   'DATA'),
  ('pyqtgraph\\examples\\DataTreeWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\DataTreeWidget.py',
   'DATA'),
  ('pyqtgraph\\examples\\DateAxisItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\DateAxisItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\DateAxisItem_QtDesigner.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\DateAxisItem_QtDesigner.py',
   'DATA'),
  ('pyqtgraph\\examples\\DiffTreeWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\DiffTreeWidget.py',
   'DATA'),
  ('pyqtgraph\\examples\\Draw.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\Draw.py',
   'DATA'),
  ('pyqtgraph\\examples\\ErrorBarItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ErrorBarItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\ExampleApp.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ExampleApp.py',
   'DATA'),
  ('pyqtgraph\\examples\\FillBetweenItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\FillBetweenItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\Flowchart.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\Flowchart.py',
   'DATA'),
  ('pyqtgraph\\examples\\FlowchartCustomNode.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\FlowchartCustomNode.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLBarGraphItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLBarGraphItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLGradientLegendItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLGradientLegendItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLGraphItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLGraphItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLImageItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLImageItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLIsosurface.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLIsosurface.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLLinePlotItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLLinePlotItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLMeshItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLMeshItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLPainterItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLPainterItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLScatterPlotItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLScatterPlotItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLSurfacePlot.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLSurfacePlot.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLTextItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLTextItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLViewWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLViewWidget.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLVolumeItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLVolumeItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GLshaders.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GLshaders.py',
   'DATA'),
  ('pyqtgraph\\examples\\GradientEditor.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GradientEditor.py',
   'DATA'),
  ('pyqtgraph\\examples\\GradientWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GradientWidget.py',
   'DATA'),
  ('pyqtgraph\\examples\\GraphItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GraphItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\GraphicsLayout.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GraphicsLayout.py',
   'DATA'),
  ('pyqtgraph\\examples\\GraphicsScene.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\GraphicsScene.py',
   'DATA'),
  ('pyqtgraph\\examples\\HistogramLUT.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\HistogramLUT.py',
   'DATA'),
  ('pyqtgraph\\examples\\ImageItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ImageItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\ImageView.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ImageView.py',
   'DATA'),
  ('pyqtgraph\\examples\\InfiniteLine.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\InfiniteLine.py',
   'DATA'),
  ('pyqtgraph\\examples\\InteractiveParameter.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\InteractiveParameter.py',
   'DATA'),
  ('pyqtgraph\\examples\\JoystickButton.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\JoystickButton.py',
   'DATA'),
  ('pyqtgraph\\examples\\Legend.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\Legend.py',
   'DATA'),
  ('pyqtgraph\\examples\\LogPlotTest.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\LogPlotTest.py',
   'DATA'),
  ('pyqtgraph\\examples\\MatrixDisplayExample.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\MatrixDisplayExample.py',
   'DATA'),
  ('pyqtgraph\\examples\\MouseSelection.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\MouseSelection.py',
   'DATA'),
  ('pyqtgraph\\examples\\MultiDataPlot.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\MultiDataPlot.py',
   'DATA'),
  ('pyqtgraph\\examples\\MultiPlotSpeedTest.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\MultiPlotSpeedTest.py',
   'DATA'),
  ('pyqtgraph\\examples\\MultiplePlotAxes.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\MultiplePlotAxes.py',
   'DATA'),
  ('pyqtgraph\\examples\\NonUniformImage.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\NonUniformImage.py',
   'DATA'),
  ('pyqtgraph\\examples\\PColorMeshItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\PColorMeshItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\PanningPlot.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\PanningPlot.py',
   'DATA'),
  ('pyqtgraph\\examples\\PlotAutoRange.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\PlotAutoRange.py',
   'DATA'),
  ('pyqtgraph\\examples\\PlotSpeedTest.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\PlotSpeedTest.py',
   'DATA'),
  ('pyqtgraph\\examples\\PlotWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\PlotWidget.py',
   'DATA'),
  ('pyqtgraph\\examples\\Plotting.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\Plotting.py',
   'DATA'),
  ('pyqtgraph\\examples\\ProgressDialog.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ProgressDialog.py',
   'DATA'),
  ('pyqtgraph\\examples\\ROIExamples.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ROIExamples.py',
   'DATA'),
  ('pyqtgraph\\examples\\ROItypes.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ROItypes.py',
   'DATA'),
  ('pyqtgraph\\examples\\RemoteGraphicsView.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\RemoteGraphicsView.py',
   'DATA'),
  ('pyqtgraph\\examples\\RemoteSpeedTest.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\RemoteSpeedTest.py',
   'DATA'),
  ('pyqtgraph\\examples\\RunExampleApp.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\RunExampleApp.py',
   'DATA'),
  ('pyqtgraph\\examples\\ScaleBar.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ScaleBar.py',
   'DATA'),
  ('pyqtgraph\\examples\\ScatterPlot.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ScatterPlot.py',
   'DATA'),
  ('pyqtgraph\\examples\\ScatterPlotSpeedTest.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ScatterPlotSpeedTest.py',
   'DATA'),
  ('pyqtgraph\\examples\\ScatterPlotWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ScatterPlotWidget.py',
   'DATA'),
  ('pyqtgraph\\examples\\SimplePlot.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\SimplePlot.py',
   'DATA'),
  ('pyqtgraph\\examples\\SpinBox.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\SpinBox.py',
   'DATA'),
  ('pyqtgraph\\examples\\Symbols.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\Symbols.py',
   'DATA'),
  ('pyqtgraph\\examples\\TableWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\TableWidget.py',
   'DATA'),
  ('pyqtgraph\\examples\\TreeWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\TreeWidget.py',
   'DATA'),
  ('pyqtgraph\\examples\\VideoSpeedTest.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\VideoSpeedTest.py',
   'DATA'),
  ('pyqtgraph\\examples\\VideoTemplate_generic.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\VideoTemplate_generic.py',
   'DATA'),
  ('pyqtgraph\\examples\\ViewBox.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ViewBox.py',
   'DATA'),
  ('pyqtgraph\\examples\\ViewBoxFeatures.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ViewBoxFeatures.py',
   'DATA'),
  ('pyqtgraph\\examples\\ViewLimits.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\ViewLimits.py',
   'DATA'),
  ('pyqtgraph\\examples\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\__init__.py',
   'DATA'),
  ('pyqtgraph\\examples\\__main__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\__main__.py',
   'DATA'),
  ('pyqtgraph\\examples\\_buildParamTypes.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\_buildParamTypes.py',
   'DATA'),
  ('pyqtgraph\\examples\\_paramtreecfg.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\_paramtreecfg.py',
   'DATA'),
  ('pyqtgraph\\examples\\beeswarm.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\beeswarm.py',
   'DATA'),
  ('pyqtgraph\\examples\\colorMaps.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\colorMaps.py',
   'DATA'),
  ('pyqtgraph\\examples\\colorMapsLinearized.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\colorMapsLinearized.py',
   'DATA'),
  ('pyqtgraph\\examples\\console_exception_inspection.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\console_exception_inspection.py',
   'DATA'),
  ('pyqtgraph\\examples\\contextMenu.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\contextMenu.py',
   'DATA'),
  ('pyqtgraph\\examples\\crosshair.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\crosshair.py',
   'DATA'),
  ('pyqtgraph\\examples\\customGraphicsItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\customGraphicsItem.py',
   'DATA'),
  ('pyqtgraph\\examples\\customPlot.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\customPlot.py',
   'DATA'),
  ('pyqtgraph\\examples\\cx_freeze\\plotTest.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\cx_freeze\\plotTest.py',
   'DATA'),
  ('pyqtgraph\\examples\\cx_freeze\\setup.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\cx_freeze\\setup.py',
   'DATA'),
  ('pyqtgraph\\examples\\designerExample.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\designerExample.py',
   'DATA'),
  ('pyqtgraph\\examples\\dockarea.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\dockarea.py',
   'DATA'),
  ('pyqtgraph\\examples\\exampleLoaderTemplate_generic.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\exampleLoaderTemplate_generic.py',
   'DATA'),
  ('pyqtgraph\\examples\\fractal.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\fractal.py',
   'DATA'),
  ('pyqtgraph\\examples\\glow.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\glow.py',
   'DATA'),
  ('pyqtgraph\\examples\\hdf5.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\hdf5.py',
   'DATA'),
  ('pyqtgraph\\examples\\histogram.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\histogram.py',
   'DATA'),
  ('pyqtgraph\\examples\\imageAnalysis.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\imageAnalysis.py',
   'DATA'),
  ('pyqtgraph\\examples\\infiniteline_performance.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\infiniteline_performance.py',
   'DATA'),
  ('pyqtgraph\\examples\\isocurve.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\isocurve.py',
   'DATA'),
  ('pyqtgraph\\examples\\jupyter_console_example.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\jupyter_console_example.py',
   'DATA'),
  ('pyqtgraph\\examples\\linkedViews.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\linkedViews.py',
   'DATA'),
  ('pyqtgraph\\examples\\logAxis.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\logAxis.py',
   'DATA'),
  ('pyqtgraph\\examples\\multiplePlotSpeedTest.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\multiplePlotSpeedTest.py',
   'DATA'),
  ('pyqtgraph\\examples\\multiprocess.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\multiprocess.py',
   'DATA'),
  ('pyqtgraph\\examples\\optics\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\optics\\__init__.py',
   'DATA'),
  ('pyqtgraph\\examples\\optics\\pyoptic.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\optics\\pyoptic.py',
   'DATA'),
  ('pyqtgraph\\examples\\optics\\schott_glasses.csv.gz',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\optics\\schott_glasses.csv.gz',
   'DATA'),
  ('pyqtgraph\\examples\\optics_demos.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\optics_demos.py',
   'DATA'),
  ('pyqtgraph\\examples\\parallelize.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\parallelize.py',
   'DATA'),
  ('pyqtgraph\\examples\\parametertree.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\parametertree.py',
   'DATA'),
  ('pyqtgraph\\examples\\py2exe\\plotTest.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\py2exe\\plotTest.py',
   'DATA'),
  ('pyqtgraph\\examples\\py2exe\\setup.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\py2exe\\setup.py',
   'DATA'),
  ('pyqtgraph\\examples\\relativity\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\relativity\\__init__.py',
   'DATA'),
  ('pyqtgraph\\examples\\relativity\\presets\\Grid Expansion.cfg',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\relativity\\presets\\Grid '
   'Expansion.cfg',
   'DATA'),
  ('pyqtgraph\\examples\\relativity\\presets\\Twin Paradox (grid).cfg',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\relativity\\presets\\Twin '
   'Paradox (grid).cfg',
   'DATA'),
  ('pyqtgraph\\examples\\relativity\\presets\\Twin Paradox.cfg',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\relativity\\presets\\Twin '
   'Paradox.cfg',
   'DATA'),
  ('pyqtgraph\\examples\\relativity\\relativity.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\relativity\\relativity.py',
   'DATA'),
  ('pyqtgraph\\examples\\relativity_demo.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\relativity_demo.py',
   'DATA'),
  ('pyqtgraph\\examples\\scrollingPlots.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\scrollingPlots.py',
   'DATA'),
  ('pyqtgraph\\examples\\syntax.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\syntax.py',
   'DATA'),
  ('pyqtgraph\\examples\\template.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\template.py',
   'DATA'),
  ('pyqtgraph\\examples\\test_examples.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\test_examples.py',
   'DATA'),
  ('pyqtgraph\\examples\\text.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\text.py',
   'DATA'),
  ('pyqtgraph\\examples\\utils.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\utils.py',
   'DATA'),
  ('pyqtgraph\\examples\\verlet_chain\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\verlet_chain\\__init__.py',
   'DATA'),
  ('pyqtgraph\\examples\\verlet_chain\\chain.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\verlet_chain\\chain.py',
   'DATA'),
  ('pyqtgraph\\examples\\verlet_chain\\relax.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\verlet_chain\\relax.py',
   'DATA'),
  ('pyqtgraph\\examples\\verlet_chain_demo.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\examples\\verlet_chain_demo.py',
   'DATA'),
  ('pyqtgraph\\exceptionHandling.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exceptionHandling.py',
   'DATA'),
  ('pyqtgraph\\exporters\\CSVExporter.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\CSVExporter.py',
   'DATA'),
  ('pyqtgraph\\exporters\\Exporter.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\Exporter.py',
   'DATA'),
  ('pyqtgraph\\exporters\\HDF5Exporter.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\HDF5Exporter.py',
   'DATA'),
  ('pyqtgraph\\exporters\\ImageExporter.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\ImageExporter.py',
   'DATA'),
  ('pyqtgraph\\exporters\\Matplotlib.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\Matplotlib.py',
   'DATA'),
  ('pyqtgraph\\exporters\\PrintExporter.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\PrintExporter.py',
   'DATA'),
  ('pyqtgraph\\exporters\\SVGExporter.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\SVGExporter.py',
   'DATA'),
  ('pyqtgraph\\exporters\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\__init__.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\Flowchart.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\Flowchart.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\FlowchartCtrlTemplate_generic.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\FlowchartCtrlTemplate_generic.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\FlowchartGraphicsView.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\FlowchartGraphicsView.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\Node.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\Node.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\NodeLibrary.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\NodeLibrary.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\Terminal.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\Terminal.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\__init__.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\library\\Data.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\Data.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\library\\Display.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\Display.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\library\\Filters.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\Filters.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\library\\Operators.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\Operators.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\library\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\__init__.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\library\\common.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\common.py',
   'DATA'),
  ('pyqtgraph\\flowchart\\library\\functions.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\functions.py',
   'DATA'),
  ('pyqtgraph\\frozenSupport.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\frozenSupport.py',
   'DATA'),
  ('pyqtgraph\\functions.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\functions.py',
   'DATA'),
  ('pyqtgraph\\functions_numba.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\functions_numba.py',
   'DATA'),
  ('pyqtgraph\\functions_qimage.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\functions_qimage.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ArrowItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ArrowItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\AxisItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\AxisItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\BarGraphItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\BarGraphItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ButtonItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ButtonItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ColorBarItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ColorBarItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\CurvePoint.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\CurvePoint.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\DateAxisItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\DateAxisItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ErrorBarItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ErrorBarItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\FillBetweenItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\FillBetweenItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\GradientEditorItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GradientEditorItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\GradientLegend.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GradientLegend.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\GradientPresets.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GradientPresets.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\GraphItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\GraphicsItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\GraphicsLayout.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsLayout.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\GraphicsObject.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsObject.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\GraphicsWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsWidget.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\GraphicsWidgetAnchor.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsWidgetAnchor.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\GridItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GridItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\HistogramLUTItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\HistogramLUTItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ImageItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ImageItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\InfiniteLine.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\InfiniteLine.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\IsocurveItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\IsocurveItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ItemGroup.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ItemGroup.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\LabelItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\LabelItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\LegendItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\LegendItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\LinearRegionItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\LinearRegionItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\MultiPlotItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\MultiPlotItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\NonUniformImage.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\NonUniformImage.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\PColorMeshItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PColorMeshItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\PlotCurveItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotCurveItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\PlotDataItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotDataItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\PlotItem\\PlotItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotItem\\PlotItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\PlotItem\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotItem\\__init__.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\PlotItem\\plotConfigTemplate_generic.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotItem\\plotConfigTemplate_generic.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ROI.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ROI.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ScaleBar.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ScaleBar.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ScatterPlotItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ScatterPlotItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\TargetItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\TargetItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\TextItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\TextItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\UIGraphicsItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\UIGraphicsItem.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\VTickGroup.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\VTickGroup.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ViewBox\\ViewBox.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\ViewBox.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ViewBox\\ViewBoxMenu.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\ViewBoxMenu.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ViewBox\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\__init__.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\ViewBox\\axisCtrlTemplate_generic.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\axisCtrlTemplate_generic.py',
   'DATA'),
  ('pyqtgraph\\graphicsItems\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\__init__.py',
   'DATA'),
  ('pyqtgraph\\icons\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\__init__.py',
   'DATA'),
  ('pyqtgraph\\icons\\auto.png',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\auto.png',
   'DATA'),
  ('pyqtgraph\\icons\\ctrl.png',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\ctrl.png',
   'DATA'),
  ('pyqtgraph\\icons\\default.png',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\default.png',
   'DATA'),
  ('pyqtgraph\\icons\\icons.svg',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\icons.svg',
   'DATA'),
  ('pyqtgraph\\icons\\invisibleEye.svg',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\invisibleEye.svg',
   'DATA'),
  ('pyqtgraph\\icons\\lock.png',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\lock.png',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\peegee.svg',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\peegee.svg',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\peegee_128px.png',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\peegee_128px.png',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\<EMAIL>',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\<EMAIL>',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\peegee_192px.png',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\peegee_192px.png',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\peegee_256px.png',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\peegee_256px.png',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\<EMAIL>',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\<EMAIL>',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\peegee_512px.png',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\peegee_512px.png',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\<EMAIL>',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\<EMAIL>',
   'DATA'),
  ('pyqtgraph\\imageview\\ImageView.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\imageview\\ImageView.py',
   'DATA'),
  ('pyqtgraph\\imageview\\ImageViewTemplate_generic.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\imageview\\ImageViewTemplate_generic.py',
   'DATA'),
  ('pyqtgraph\\imageview\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\imageview\\__init__.py',
   'DATA'),
  ('pyqtgraph\\jupyter\\GraphicsView.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\jupyter\\GraphicsView.py',
   'DATA'),
  ('pyqtgraph\\jupyter\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\jupyter\\__init__.py',
   'DATA'),
  ('pyqtgraph\\metaarray\\MetaArray.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\metaarray\\MetaArray.py',
   'DATA'),
  ('pyqtgraph\\metaarray\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\metaarray\\__init__.py',
   'DATA'),
  ('pyqtgraph\\multiprocess\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\multiprocess\\__init__.py',
   'DATA'),
  ('pyqtgraph\\multiprocess\\bootstrap.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\multiprocess\\bootstrap.py',
   'DATA'),
  ('pyqtgraph\\multiprocess\\parallelizer.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\multiprocess\\parallelizer.py',
   'DATA'),
  ('pyqtgraph\\multiprocess\\processes.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\multiprocess\\processes.py',
   'DATA'),
  ('pyqtgraph\\multiprocess\\remoteproxy.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\multiprocess\\remoteproxy.py',
   'DATA'),
  ('pyqtgraph\\opengl\\GLGraphicsItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\GLGraphicsItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\GLViewWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\GLViewWidget.py',
   'DATA'),
  ('pyqtgraph\\opengl\\MeshData.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\MeshData.py',
   'DATA'),
  ('pyqtgraph\\opengl\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\__init__.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLAxisItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLAxisItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLBarGraphItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLBarGraphItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLBoxItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLBoxItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLGradientLegendItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLGradientLegendItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLGraphItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLGraphItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLGridItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLGridItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLImageItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLImageItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLLinePlotItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLLinePlotItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLMeshItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLMeshItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLScatterPlotItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLScatterPlotItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLSurfacePlotItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLSurfacePlotItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLTextItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLTextItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\GLVolumeItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\GLVolumeItem.py',
   'DATA'),
  ('pyqtgraph\\opengl\\items\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\items\\__init__.py',
   'DATA'),
  ('pyqtgraph\\opengl\\shaders.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\opengl\\shaders.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\Parameter.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\Parameter.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\ParameterItem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\ParameterItem.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\ParameterSystem.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\ParameterSystem.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\ParameterTree.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\ParameterTree.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\SystemSolver.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\SystemSolver.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\__init__.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\interactive.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\interactive.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\__init__.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\action.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\action.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\actiongroup.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\actiongroup.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\basetypes.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\basetypes.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\bool.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\bool.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\calendar.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\calendar.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\checklist.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\checklist.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\color.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\color.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\colormap.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\colormap.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\colormaplut.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\colormaplut.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\file.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\file.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\font.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\font.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\list.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\list.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\numeric.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\numeric.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\pen.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\pen.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\progress.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\progress.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\qtenum.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\qtenum.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\slider.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\slider.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\str.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\str.py',
   'DATA'),
  ('pyqtgraph\\parametertree\\parameterTypes\\text.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\text.py',
   'DATA'),
  ('pyqtgraph\\reload.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\reload.py',
   'DATA'),
  ('pyqtgraph\\units.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\units.py',
   'DATA'),
  ('pyqtgraph\\util\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\__init__.py',
   'DATA'),
  ('pyqtgraph\\util\\colorama\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\colorama\\__init__.py',
   'DATA'),
  ('pyqtgraph\\util\\colorama\\win32.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\colorama\\win32.py',
   'DATA'),
  ('pyqtgraph\\util\\colorama\\winterm.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\colorama\\winterm.py',
   'DATA'),
  ('pyqtgraph\\util\\cprint.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\cprint.py',
   'DATA'),
  ('pyqtgraph\\util\\cupy_helper.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\cupy_helper.py',
   'DATA'),
  ('pyqtgraph\\util\\garbage_collector.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\garbage_collector.py',
   'DATA'),
  ('pyqtgraph\\util\\get_resolution.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\get_resolution.py',
   'DATA'),
  ('pyqtgraph\\util\\glinfo.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\glinfo.py',
   'DATA'),
  ('pyqtgraph\\util\\mutex.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\mutex.py',
   'DATA'),
  ('pyqtgraph\\util\\numba_helper.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\numba_helper.py',
   'DATA'),
  ('pyqtgraph\\widgets\\BusyCursor.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\BusyCursor.py',
   'DATA'),
  ('pyqtgraph\\widgets\\CheckTable.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\CheckTable.py',
   'DATA'),
  ('pyqtgraph\\widgets\\ColorButton.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ColorButton.py',
   'DATA'),
  ('pyqtgraph\\widgets\\ColorMapButton.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ColorMapButton.py',
   'DATA'),
  ('pyqtgraph\\widgets\\ColorMapMenu.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ColorMapMenu.py',
   'DATA'),
  ('pyqtgraph\\widgets\\ColorMapWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ColorMapWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\ComboBox.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ComboBox.py',
   'DATA'),
  ('pyqtgraph\\widgets\\DataFilterWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\DataFilterWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\DataTreeWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\DataTreeWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\DiffTreeWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\DiffTreeWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\FeedbackButton.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\FeedbackButton.py',
   'DATA'),
  ('pyqtgraph\\widgets\\FileDialog.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\FileDialog.py',
   'DATA'),
  ('pyqtgraph\\widgets\\GradientWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\GradientWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\GraphicsLayoutWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\GraphicsLayoutWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\GraphicsView.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\GraphicsView.py',
   'DATA'),
  ('pyqtgraph\\widgets\\GroupBox.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\GroupBox.py',
   'DATA'),
  ('pyqtgraph\\widgets\\HistogramLUTWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\HistogramLUTWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\JoystickButton.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\JoystickButton.py',
   'DATA'),
  ('pyqtgraph\\widgets\\LayoutWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\LayoutWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\MatplotlibWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\MatplotlibWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\MultiPlotWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\MultiPlotWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\PathButton.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\PathButton.py',
   'DATA'),
  ('pyqtgraph\\widgets\\PenPreviewLabel.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\PenPreviewLabel.py',
   'DATA'),
  ('pyqtgraph\\widgets\\PlotWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\PlotWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\ProgressDialog.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ProgressDialog.py',
   'DATA'),
  ('pyqtgraph\\widgets\\RawImageWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\RawImageWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\RemoteGraphicsView.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\RemoteGraphicsView.py',
   'DATA'),
  ('pyqtgraph\\widgets\\ScatterPlotWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ScatterPlotWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\SpinBox.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\SpinBox.py',
   'DATA'),
  ('pyqtgraph\\widgets\\TableWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\TableWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\TreeWidget.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\TreeWidget.py',
   'DATA'),
  ('pyqtgraph\\widgets\\ValueLabel.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ValueLabel.py',
   'DATA'),
  ('pyqtgraph\\widgets\\VerticalLabel.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\VerticalLabel.py',
   'DATA'),
  ('pyqtgraph\\widgets\\__init__.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\__init__.py',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('PyQt6\\uic\\widget-plugins\\qtwebenginewidgets.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\widget-plugins\\qtwebenginewidgets.py',
   'DATA'),
  ('PyQt6\\uic\\widget-plugins\\qtprintsupport.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\widget-plugins\\qtprintsupport.py',
   'DATA'),
  ('PyQt6\\uic\\widget-plugins\\qtcharts.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\widget-plugins\\qtcharts.py',
   'DATA'),
  ('PyQt6\\uic\\widget-plugins\\qaxcontainer.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\widget-plugins\\qaxcontainer.py',
   'DATA'),
  ('PyQt6\\uic\\widget-plugins\\qtopenglwidgets.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\widget-plugins\\qtopenglwidgets.py',
   'DATA'),
  ('PyQt6\\uic\\widget-plugins\\qtquickwidgets.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\widget-plugins\\qtquickwidgets.py',
   'DATA'),
  ('PyQt6\\uic\\widget-plugins\\qscintilla.py',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\uic\\widget-plugins\\qscintilla.py',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ar.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ko.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ka.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fa.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ka.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fi.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_it.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fr.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_uk.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ka.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nl.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_he.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sk.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nn.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lt.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_bg.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_da.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sl.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pl.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ca.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_cs.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ja.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hr.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sv.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_tr.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_es.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lv.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_en.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_de.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gl.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hu.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ru.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gd.qm',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gd.qm',
   'DATA'),
  ('numpy-2.3.1.dist-info\\METADATA',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy-2.3.1.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.3.1.dist-info\\INSTALLER',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy-2.3.1.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.3.1.dist-info\\entry_points.txt',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy-2.3.1.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.3.1.dist-info\\WHEEL',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy-2.3.1.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.3.1.dist-info\\RECORD',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy-2.3.1.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.3.1.dist-info\\DELVEWHEEL',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy-2.3.1.dist-info\\DELVEWHEEL',
   'DATA'),
  ('numpy-2.3.1.dist-info\\LICENSE.txt',
   'O:\\python\\shangweiji_test\\.venv\\Lib\\site-packages\\numpy-2.3.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('base_library.zip',
   'O:\\python\\shangweiji_test\\build\\TimeAndControl\\base_library.zip',
   'DATA')],
 [('io', 'I:\\Program Files\\python\\python12\\Lib\\io.py', 'PYMODULE'),
  ('reprlib',
   'I:\\Program Files\\python\\python12\\Lib\\reprlib.py',
   'PYMODULE'),
  ('linecache',
   'I:\\Program Files\\python\\python12\\Lib\\linecache.py',
   'PYMODULE'),
  ('posixpath',
   'I:\\Program Files\\python\\python12\\Lib\\posixpath.py',
   'PYMODULE'),
  ('stat', 'I:\\Program Files\\python\\python12\\Lib\\stat.py', 'PYMODULE'),
  ('enum', 'I:\\Program Files\\python\\python12\\Lib\\enum.py', 'PYMODULE'),
  ('collections.abc',
   'I:\\Program Files\\python\\python12\\Lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'I:\\Program Files\\python\\python12\\Lib\\collections\\__init__.py',
   'PYMODULE'),
  ('functools',
   'I:\\Program Files\\python\\python12\\Lib\\functools.py',
   'PYMODULE'),
  ('os', 'I:\\Program Files\\python\\python12\\Lib\\os.py', 'PYMODULE'),
  ('ntpath', 'I:\\Program Files\\python\\python12\\Lib\\ntpath.py', 'PYMODULE'),
  ('genericpath',
   'I:\\Program Files\\python\\python12\\Lib\\genericpath.py',
   'PYMODULE'),
  ('codecs', 'I:\\Program Files\\python\\python12\\Lib\\codecs.py', 'PYMODULE'),
  ('types', 'I:\\Program Files\\python\\python12\\Lib\\types.py', 'PYMODULE'),
  ('_collections_abc',
   'I:\\Program Files\\python\\python12\\Lib\\_collections_abc.py',
   'PYMODULE'),
  ('sre_compile',
   'I:\\Program Files\\python\\python12\\Lib\\sre_compile.py',
   'PYMODULE'),
  ('warnings',
   'I:\\Program Files\\python\\python12\\Lib\\warnings.py',
   'PYMODULE'),
  ('traceback',
   'I:\\Program Files\\python\\python12\\Lib\\traceback.py',
   'PYMODULE'),
  ('locale', 'I:\\Program Files\\python\\python12\\Lib\\locale.py', 'PYMODULE'),
  ('weakref',
   'I:\\Program Files\\python\\python12\\Lib\\weakref.py',
   'PYMODULE'),
  ('sre_constants',
   'I:\\Program Files\\python\\python12\\Lib\\sre_constants.py',
   'PYMODULE'),
  ('keyword',
   'I:\\Program Files\\python\\python12\\Lib\\keyword.py',
   'PYMODULE'),
  ('encodings.zlib_codec',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'I:\\Program Files\\python\\python12\\Lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('abc', 'I:\\Program Files\\python\\python12\\Lib\\abc.py', 'PYMODULE'),
  ('heapq', 'I:\\Program Files\\python\\python12\\Lib\\heapq.py', 'PYMODULE'),
  ('sre_parse',
   'I:\\Program Files\\python\\python12\\Lib\\sre_parse.py',
   'PYMODULE'),
  ('re._parser',
   'I:\\Program Files\\python\\python12\\Lib\\re\\_parser.py',
   'PYMODULE'),
  ('re._constants',
   'I:\\Program Files\\python\\python12\\Lib\\re\\_constants.py',
   'PYMODULE'),
  ('re._compiler',
   'I:\\Program Files\\python\\python12\\Lib\\re\\_compiler.py',
   'PYMODULE'),
  ('re._casefix',
   'I:\\Program Files\\python\\python12\\Lib\\re\\_casefix.py',
   'PYMODULE'),
  ('re',
   'I:\\Program Files\\python\\python12\\Lib\\re\\__init__.py',
   'PYMODULE'),
  ('copyreg',
   'I:\\Program Files\\python\\python12\\Lib\\copyreg.py',
   'PYMODULE'),
  ('_weakrefset',
   'I:\\Program Files\\python\\python12\\Lib\\_weakrefset.py',
   'PYMODULE'),
  ('operator',
   'I:\\Program Files\\python\\python12\\Lib\\operator.py',
   'PYMODULE')])
