MAIN_STYLESHEET = """
/* --- 基本样式重置 --- */
* {
    outline: none;
}

/* --- 主窗口和容器 --- */
QMainWindow {
    background-color: #2e2e2e;
    color: #e0e0e0;
    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
    font-size: 14px;
}

QWidget#CentralWidget {
    background-color: #424242;
    border: 1px solid #2a2a2a;
    margin: 0px;
}



/* --- 左侧面板 --- */
QWidget#LeftPanel {
    background-color: #424242;
    padding: 15px;
}

/* --- 右侧面板 --- */
QWidget#RightPanel {
    background-color: #333333;
    border-left: 1px solid #2a2a2a;
    padding: 20px;
    min-width: 280px;
    max-width: 280px;
}

/* --- 通用按钮样式 --- */
QPushButton {
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 600;
    color: white;
    background-color: #555555;
}

QPushButton:hover {
    background-color: #666666;
}

QPushButton:pressed {
    background-color: #444444;
}

/* --- 顶部菜单按钮 --- */
QPushButton#FileBtn {
    background-color: #3a589e;
    padding-right: 28px;
    min-width: 60px;
}

QPushButton#FileBtn:hover {
    background-color: #4165a8;
}

QPushButton#FileBtn::menu-indicator {
    image: none; /* 隐藏默认箭头 */
}

QPushButton#SettingsBtn {
    background-color: #e69a28;
    min-width: 80px;
}

QPushButton#SettingsBtn:hover {
    background-color: #f0a838;
}

QMenu {
    background-color: #3a589e;
    border: 1px solid #2a2a2a;
    color: white;
    padding: 5px;
}

QMenu::item {
    padding: 8px 15px;
    border-radius: 3px;
}

QMenu::item:selected {
    background-color: #4165a8;
}

/* --- 图表控制按钮 --- */
QPushButton#StartBtn {
    background-color: #1d72c2;
    padding: 10px 30px;
    font-size: 16px;
}
QPushButton#StartBtn:hover { background-color: #2082d2; }

QPushButton#StopBtn {
    background-color: #d9534f;
    padding: 10px 30px;
    font-size: 16px;
}
QPushButton#StopBtn:hover { background-color: #e9635f; }

QPushButton#ClearBtn {
    background-color: #e69a28;
    padding: 10px 30px;
    font-size: 16px;
}
QPushButton#ClearBtn:hover { background-color: #f0a838; }

/* --- 连接按钮 --- */
QPushButton#ConnectBtn {
    background-color: #6c757d;
    min-width: 80px;
}

QPushButton#ConnectBtn:hover {
    background-color: #7c858d;
}

/* --- 主控制按钮 --- */
QPushButton#MasterOffBtn {
    background-color: #d9534f;
    padding: 12px;
    font-size: 16px;
    font-weight: bold;
}

QPushButton#MasterOffBtn:hover {
    background-color: #e9635f;
}

QPushButton#MasterOnBtn {
    background-color: #5cb85c;
    padding: 12px;
    font-size: 16px;
    font-weight: bold;
}

QPushButton#MasterOnBtn:hover {
    background-color: #6cc86c;
}

QPushButton#ResistanceToggle {
    background-color: #a0a0a0;
    color: #d0d0d0;
    border: 1px solid #888888;
    padding: 8px;
    font-size: 14px;
    font-weight: bold;
    min-width: 100px;
}

QPushButton#ResistanceToggle:checked {
    background-color: #5cb85c;
    color: white;
}

QPushButton#ResistanceToggle:hover {
    background-color: #b0b0b0;
}

QPushButton#ResistanceToggle:checked:hover {
    background-color: #6cc86c;
}

/* --- 控制框 --- */
QGroupBox#ControlBox {
    background-color: #4a4a4a;
    border: none;
    border-radius: 5px;
    padding: 15px;
    margin: 0px 35px;  /* 大胆增加左右边距让控制框更窄 */
}

QGroupBox#ControlBox::title {
    color: transparent; /* 隐藏标题 */
}

/* --- 控制项样式 --- */
QWidget#ControlItem {
    margin: 0px;
    padding: 0px;
}

/* --- 标签样式 --- */
QLabel {
    color: #e0e0e0;
    font-weight: bold;
    font-size: 16px;
}

QLabel#ControlLabel {
    color: #e0e0e0;
    font-weight: bold;
    font-size: 16px;
    text-align: center;
}

QLabel#StatusLabel {
    color: #b0b0b0;
    font-size: 14px;
    font-weight: normal;
}

QLabel#UnitLabel {
    color: #e0e0e0;
    font-weight: normal;
    font-size: 16px;
    margin-left: 5px;
}

/* --- 输入框和显示框 --- */
QLineEdit {
    background-color: #00aaff;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 15px;
    font-weight: bold;
    font-size: 18px;
    text-align: center;
    min-width: 100px;
    min-height: 25px;
}

QLineEdit:focus {
    background-color: #1ac0ff;
}

QLineEdit:read-only {
    background-color: #555555;
    color: #d0d0d0;
}

QLineEdit#EmptyDisplay {
    background-color: #00aaff;
    color: #ffffff;
    min-height: 25px;
    text-align: center;
}

/* --- 下拉框样式 --- */
QComboBox {
    background-color: #555555;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 10px;
}

QComboBox:hover {
    background-color: #666666;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: url(down_arrow.png); /* 需要一个箭头图片 */
}

QComboBox QAbstractItemView {
    background-color: #555555;
    border: 1px solid #777777;
    color: white;
    selection-background-color: #00aaff;
}

/* --- 状态栏 --- */
QLabel#StatusBar {
    background-color: #2e2e2e;
    color: #cccccc;
    font-size: 13px;
    padding: 5px 15px;
    border-top: 1px solid #2a2a2a;
}

/* --- 绘图区域样式 --- */
PlotWidget {
    background-color: #2a2a2a;
    border: 1px solid #777777;
}

/* --- 设置对话框样式 --- */
#SettingsDialog {
    background-color: transparent;
}

#SettingsBackgroundFrame {
    background-color: #3c3c3c;
    border: 1px solid #e69a28;
    border-radius: 8px;
    color: #e0e0e0;
    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
}

#SettingsTitle {
    color: #e69a28;
    font-size: 16px;
    font-weight: bold;
    padding: 10px;
}

#SettingsCloseButton {
    background-color: transparent;
    color: #aaaaaa;
    border: none;
    font-size: 20px;
    font-weight: bold;
    min-width: 30px;
    max-width: 30px;
}

#SettingsCloseButton:hover {
    color: #ffffff;
}

#SettingsDialog QLabel {
    color: #e0e0e0;
    font-weight: normal;
    font-size: 15px;
}

#SettingsInput {
    background-color: #555;
    border: 1px solid #777;
    color: #fff;
    border-radius: 4px;
    padding: 6px 10px;
    font-size: 15px;
    font-weight: normal;
}

#SettingsInput:focus {
    border: 1px solid #00aaff;
    background-color: #666;
}

#SettingsOKButton {
    background-color: #e69a28;
    font-size: 15px;
    font-weight: bold;
    padding: 8px 35px;
    min-width: 100px;
}

#SettingsOKButton:hover {
    background-color: #f0a838;
}

#SettingsDialog QComboBox {
    background-color: #555;
    border: 1px solid #777;
    color: #fff;
    border-radius: 4px;
    padding: 5px 10px; /* Adjusted padding */
    font-size: 15px;
    font-weight: normal;
}

#SettingsDialog QComboBox:hover {
    border: 1px solid #999;
}

#SettingsDialog QComboBox::drop-down {
    border: none;
    background-color: transparent;
    width: 20px;
}

#SettingsDialog QComboBox::down-arrow {
    /* A simple CSS triangle arrow */
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 5px solid #ccc;
    width: 0px;
    height: 0px;
    margin: auto;
}

#SettingsDialog QComboBox QAbstractItemView {
    background-color: #555;
    border: 1px solid #777;
    color: white;
    selection-background-color: #00aaff;
}

/* Checkable Toggle Button for Resistance Measurement */
#ToggleButton {
    /* Inherits general QPushButton style */
    background-color: #555; /* Default OFF color */
}

#ToggleButton:checked {
    background-color: #3a76c4; /* A nice blue for ON state */
    color: #fff;
}

#ToggleButton:disabled {
    background-color: #484848;
    color: #888;
}

/* --- Settings Dialog --- */
#SettingsDialog {
    background-color: transparent;
}

#SettingsBackgroundFrame {
    background-color: #3c3c3c;
    border: 1px solid #555;
    border-radius: 8px;
}

#SettingsTitle {
    color: #ddd;
    font-size: 16px;
    font-weight: bold;
    padding-top: 5px;
}

#SettingsCloseButton {
    background-color: transparent;
    color: #aaa;
    border: none;
    font-size: 18px;
    font-weight: bold;
    padding: 0px 5px 5px 5px;
}

#SettingsCloseButton:hover {
    color: #fff;
}

.QLabel {
    color: #ccc;
    font-size: 14px;
}

#SettingsInput {
    background-color: #2c2c2c;
    border: 1px solid #555;
    color: #ddd;
    padding: 5px;
    font-size: 14px;
    min-width: 80px;
}

#SettingsOKButton {
    background-color: #0078d4;
    color: white;
    border: 1px solid #005a9e;
    padding: 8px 30px;
    font-size: 14px;
}

#SettingsOKButton:hover {
    background-color: #008cff;
}

#SettingsOKButton:pressed {
    background-color: #005a9e;
}
"""
