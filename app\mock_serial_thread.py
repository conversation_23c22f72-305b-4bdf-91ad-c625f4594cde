import time
import random
import struct
from PyQt6.QtCore import QThread, pyqtSignal

class MockSerialThread(QThread):
    """
    A mock serial thread that simulates the real hardware for testing purposes.
    It emits the same signals as the real SerialThread.
    """
    # Signals must match SerialThread exactly
    data_received = pyqtSignal(list)
    status_updated = pyqtSignal(dict)
    resistance_received = pyqtSignal(int)
    port_list_updated = pyqtSignal(list)
    connection_status = pyqtSignal(bool)
    error_message = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.is_running = False
        self._plot_started = False
        self._port = "COM_TEST"
        self._connected = False
        
        # 初始化所有状态属性，与界面初始值一致
        self.duty_cycle = 12  # 1.2%
        self.heating_time = 101 # 1.01s
        self.on_off_status = False
        self.heating_status = False
        self.resistance = 15000 # e.g. 150.00 Ohm, store it internally
        self.time_since_plot_start = 0.0

    @property
    def is_connected(self):
        return self._connected

    def connect_port(self, port_name):
        """连接到指定端口"""
        self._port = port_name
        self._connected = True
        self.connection_status.emit(True)
        if not self.isRunning():
            self.start()

    def disconnect_port(self):
        """断开连接"""
        self._connected = False
        self.is_running = False
        self._plot_started = False
        self.connection_status.emit(False)

    def send_data(self, data: bytes):
        """
        处理从主窗口发送的命令数据.
        解析简单命令和带值的命令，并更新模拟器的状态.
        """
        if not self._connected:
            return

        try:
            # 尝试解码为简单的ASCII命令
            cmd_str = data.decode('ascii').strip()
            
            if cmd_str == "A 1":
                self._plot_started = True
            elif cmd_str == "A 0":
                self._plot_started = False
            elif cmd_str == "B 1":
                # When measurement is requested, update the internal value and emit it
                self.resistance = int(15000 + random.uniform(-500, 500))
                self.resistance_received.emit(self.resistance)
            elif cmd_str == "B 0":
                pass # This might not be needed anymore
            elif cmd_str == "E 1":
                self.heating_status = True
            elif cmd_str == "E 0":
                self.heating_status = False
            elif cmd_str == "F 1":
                # 响应数据请求，发送20个温度数据点
                if self._plot_started:
                    self.emit_plot_data()
            elif cmd_str == "SSS":
                self.emit_status_data()

        except UnicodeDecodeError:
            # 如果解码失败，则假定是带值的命令 (例如 'C' 或 'D')
            if len(data) >= 4 and data[1:2] == b' ':
                cmd_char = data[0:1].decode('ascii')
                value = struct.unpack('<H', data[2:4])[0]
                if cmd_char == 'C':
                    self.duty_cycle = value
                elif cmd_char == 'D':
                    self.heating_time = value

    def scan_for_ports(self):
        """扫描可用端口"""
        # 发出模拟端口
        self.port_list_updated.emit([self._port])

    def emit_status_data(self):
        """发出模拟的状态数据"""
        # duty_cycle和heating_time小幅波动（模拟硬件抖动，但主窗口输入后以主窗口为准）
        duty_cycle = self.duty_cycle + random.randint(-1, 1)
        heating_time = self.heating_time + random.randint(-1, 1)
        status = {
            "plot_status": self._plot_started,
            "duty_cycle": duty_cycle,
            "heating_time": heating_time,
            "on_off_status": self.on_off_status,
            "resistance": self.resistance, # Now returns the stored value
            "heating_status": self.heating_status,
            "max_plot_time": 1000, # Corresponds to 100.0s
            "max_temp": 3600,
            "min_temp": 400
        }
        self.status_updated.emit(status)

    def emit_plot_data(self):
        """
        生成并发出温度数据点列表.
        恢复20个数据点的协议，每个数据点代表5ms间隔
        """
        if not self._plot_started:
            return
            
        temps = []
        # 生成20个数据点，每个代表5ms间隔的数据
        for i in range(20):
            # 计算每个数据点的时间偏移
            point_time = self.time_since_plot_start + (i * 0.005)
            
            # 模拟一个更真实的温度曲线：快速上升后缓慢下降
            t = point_time % 4.0 # 模拟一个4秒的周期
            
            base_temp = 300.0
            peak_temp = 4000.0
            
            # 如果加热状态为ON，则产生一个峰值，否则温度逐渐降低
            if self.heating_status:
                 # 模拟一个快速上升然后衰减的脉冲
                if t < 0.2: # 快速上升
                    current_temp = base_temp + (peak_temp - base_temp) * (t / 0.2)
                else: # 指数衰减
                    current_temp = base_temp + (peak_temp - base_temp) * pow(2.718, -(t - 0.2) / 1.5)
            else:
                # 如果不加热，则温度在基础温度附近小幅波动
                current_temp = base_temp

            # 添加一些随机噪声
            noise = random.uniform(-25, 25)
            point_temp = current_temp + noise
            # 确保温度在合理范围内
            temps.append(int(max(250, min(4500, point_temp))))
            
        self.data_received.emit(temps)
        # 时间前进100ms（20个点 × 5ms/点）
        self.time_since_plot_start += 0.1

    def run(self):
        """主循环，现在只处理连接状态和命令响应"""
        self.is_running = True
        
        while self.is_running:
            # 模拟线程现在只响应命令，不自动发送数据
            # 这与真实硬件的行为一致
            time.sleep(0.01)  # 减少CPU使用率 