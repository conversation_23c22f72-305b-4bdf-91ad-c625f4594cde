import sys
import csv
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QPushButton, QLabel, QLineEdit, QGroupBox, QComboBox,
                             QMenu, QFileDialog, QFrame, QTextEdit)
from PyQt6.QtCore import Qt, QTimer, QSettings
from PyQt6.QtGui import QFont, QIcon
import pyqtgraph as pg
import time

from .styles import MAIN_STYLESHEET
from .settings_dialog import SettingsDialog
from .serial_thread import SerialThread
from .mock_serial_thread import MockSerialThread
from . import protocol

# 新增一个字典来映射确认码到用户友好的消息
ACK_MESSAGES = {
    b'AOK': "绘图状态更新成功",
    b'BOK': "电阻测量状态更新成功",
    b'COK': "占空比设置成功",
    b'DOK': "闪烁时间设置成功",
    b'EOK': "加热状态更新成功",
    b'HOK': "最大绘图时间设置成功",
    b'IOK': "最小温度设置成功",
    b'JOK': "最大温度设置成功",
}

class MainWindow(QMainWindow):
    def __init__(self, test_mode=False):
        super().__init__()
        self.test_mode = test_mode
        self.setWindowTitle("Time & Control v4.0")
        self.setGeometry(100, 100, 1200, 700)

        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        self.setStyleSheet(MAIN_STYLESHEET)

        # Load settings first
        self.load_settings()

        # Data storage
        self.time_data = []
        self.temp_data = []
        self.current_time = 0.0
        
        # 注意：max_plot_duration, min_temp, max_temp
        # 都在 load_settings() 中设置，response_time 已被移除

        # Initialize Serial Thread (Real or Mock)
        if test_mode:
            self.serial_thread = MockSerialThread()
        else:
            self.serial_thread = SerialThread()
        self.serial_thread.port_list_updated.connect(self.update_port_list)
        self.serial_thread.connection_status.connect(self.on_connection_status_changed)
        self.serial_thread.data_received.connect(self.update_plot)
        self.serial_thread.status_updated.connect(self.update_status_display)
        self.serial_thread.resistance_received.connect(self.update_resistance_display)
        self.serial_thread.error_message.connect(self.show_error)
        self.serial_thread.command_ack_received.connect(self.handle_command_ack)
        self.serial_thread.log_message.connect(self.append_to_log)

        # Timers
        self.plot_timer = QTimer(self)
        self.plot_timer.timeout.connect(self.request_plot_data)
        self.status_timer = QTimer(self)
        self.status_timer.timeout.connect(self.request_status)
        self.resistance_timer = QTimer(self) # New timer for resistance
        self.resistance_timer.timeout.connect(self.request_resistance_measurement)
        
        # 新增：用于等待ACK的定时器
        self.ack_timer = QTimer(self)
        self.ack_timer.setSingleShot(True)
        self.ack_timer.timeout.connect(self.on_ack_timeout)
        
        # 新增：保存当前等待的ACK和回调
        self._waiting_for_ack = None
        self._ack_success_callback = None
        self._ack_failure_callback = None
        
        self.plot_start_time = None  # 记录绘图开始的实际时间
        
        self.setup_ui()
        self.update_ui_for_connection(False)
        self.setStatusBar(None) # Remove original status bar
        
        self.serial_thread.scan_for_ports()

    def load_settings(self):
        """
        加载持久化设置, 从根目录的 settings.ini 文件.
        使用显式类型转换以提高健壮性.
        """
        self.settings = QSettings("settings.ini", QSettings.Format.IniFormat)
        
        # 使用 .value() 读取值 (可能是字符串), 然后用 Python 的 float() 和 int() 进行显式转换.
        # 这比依赖 Qt 的内部类型转换更可靠, 特别是对于 .ini 文件.
        try:
            raw_max_plot = self.settings.value("max_plot_duration", 100.0)
            raw_min_temp = self.settings.value("min_temp", 400)
            raw_max_temp = self.settings.value("max_temp", 3600)
            
            self.max_plot_duration = float(raw_max_plot)
            self.min_temp = int(raw_min_temp)
            self.max_temp = int(raw_max_temp)
            
        except (ValueError, TypeError) as e:
            # 如果转换失败 (例如.ini文件被手动改成非法值), 则使用默认值
            self.max_plot_duration = 100.0
            self.min_temp = 400
            self.max_temp = 3600

    def setup_ui(self):
        self.central_widget.setObjectName("CentralWidget")
        main_layout = QVBoxLayout(self.central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 主要内容区域 (左右布局)
        self.create_main_content(main_layout)

    def create_main_content(self, parent_layout):
        """创建主要内容区域"""
        main_content_layout = QHBoxLayout()
        main_content_layout.setContentsMargins(0, 0, 0, 0)
        main_content_layout.setSpacing(0)

        # 左侧面板
        self.create_left_panel(main_content_layout)
        
        # 右侧面板
        self.create_right_panel(main_content_layout)

        content_widget = QWidget()
        content_widget.setLayout(main_content_layout)
        parent_layout.addWidget(content_widget)

    def create_left_panel(self, parent_layout):
        """创建左侧面板"""
        left_panel = QWidget()
        left_panel.setObjectName("LeftPanel")
        left_panel_layout = QVBoxLayout(left_panel)
        left_panel_layout.setContentsMargins(0, 0, 0, 0)
        left_panel_layout.setSpacing(10)

        # 菜单栏
        self.create_menu_bar(left_panel_layout)

        # 图表区域
        self.create_graph_area(left_panel_layout)

        # 图表控制按钮
        self.create_graph_controls(left_panel_layout)

        # 状态栏
        self.create_status_bar(left_panel_layout)

        # 日志查看器
        self.create_log_viewer(left_panel_layout)

        # 调整垂直布局的拉伸因子
        left_panel_layout.setStretchFactor(self.graph_group_box, 5) # 图表是最大的
        left_panel_layout.setStretchFactor(self.log_group_box, 2)   # 日志查看器有一些空间

        parent_layout.addWidget(left_panel)

    def create_menu_bar(self, parent_layout):
        """创建菜单栏"""
        menu_layout = QHBoxLayout()
        menu_layout.setContentsMargins(0, 0, 0, 0)
        
        self.file_button = QPushButton("File ▼")
        self.file_button.setObjectName("FileBtn")
        file_menu = QMenu(self)
        file_menu.addAction("Open History...", self.open_data_file)
        file_menu.addAction("Save Data", self.save_data)
        file_menu.addAction("Exit", self.close)
        self.file_button.setMenu(file_menu)
        
        self.settings_button = QPushButton("Settings")
        self.settings_button.setObjectName("SettingsBtn")
        self.settings_button.clicked.connect(self.open_settings_dialog)
        
        menu_layout.addWidget(self.file_button)
        menu_layout.addWidget(self.settings_button)
        menu_layout.addStretch()
        
        parent_layout.addLayout(menu_layout)

    def create_graph_area(self, parent_layout):
        """创建图表区域"""
        # 设置图表
        self.plot_widget = pg.PlotWidget()
        self.plot_widget.setBackground('#2c313c')
        self.plot_widget.showGrid(x=True, y=True, alpha=0.3)
        self.plot_widget.setLabel('left', 'Temperature (C)', color='#aaa')
        self.plot_widget.setLabel('bottom', 'Time (s)', color='#aaa')
        
        # 设置坐标轴颜色
        self.plot_widget.getAxis('left').setPen('#777')
        self.plot_widget.getAxis('bottom').setPen('#777')
        self.plot_widget.getAxis('left').setTextPen('#aaa')
        self.plot_widget.getAxis('bottom').setTextPen('#aaa')
        
        # --- 关键修改：固定Y轴范围 ---
        self.plot_widget.setYRange(0, 4000)
        self.plot_widget.getPlotItem().getViewBox().setMouseEnabled(y=False) # 禁止Y轴鼠标缩放

        self.plot_curve = self.plot_widget.plot(pen=pg.mkPen('#ffff00', width=2))
        
        graph_layout = QVBoxLayout()
        graph_layout.setContentsMargins(0, 0, 0, 0)
        graph_layout.addWidget(self.plot_widget)
        self.graph_group_box = QGroupBox("图表")
        self.graph_group_box.setLayout(graph_layout)
        parent_layout.addWidget(self.graph_group_box)

    def create_graph_controls(self, parent_layout):
        """创建图表控制按钮"""
        controls_layout = QHBoxLayout()
        controls_layout.setContentsMargins(0, 10, 0, 10)
        controls_layout.addStretch()
        
        self.start_button = QPushButton("Start")
        self.start_button.setObjectName("StartBtn")
        self.start_button.clicked.connect(self.start_plotting)
        
        self.stop_button = QPushButton("Stop")
        self.stop_button.setObjectName("StopBtn")
        self.stop_button.clicked.connect(self.stop_plotting)
        
        self.clear_button = QPushButton("Clear")
        self.clear_button.setObjectName("ClearBtn")
        self.clear_button.clicked.connect(self.clear_plot)
        
        controls_layout.addWidget(self.start_button)
        controls_layout.addWidget(self.stop_button)
        controls_layout.addWidget(self.clear_button)
        controls_layout.addStretch()
        
        parent_layout.addLayout(controls_layout)

    def create_status_bar(self, parent_layout):
        """创建状态栏"""
        self.status_label = QLabel("Disconnected.")
        self.status_label.setObjectName("StatusBar")
        parent_layout.addWidget(self.status_label)

    def create_right_panel(self, parent_layout):
        """创建右侧面板"""
        right_panel = QWidget()
        right_panel.setObjectName("RightPanel")
        right_panel_layout = QVBoxLayout(right_panel)
        right_panel_layout.setContentsMargins(0, 15, 0, 15)
        right_panel_layout.setSpacing(20)

        # 连接区域
        self.create_connection_section(right_panel_layout)

        # 第一个控制框 (Duty Ratio & Flash Time)
        self.create_first_control_box(right_panel_layout)

        # 第二个控制框 (R-Ω & On/Off)
        self.create_second_control_box(right_panel_layout)

        # 主控制按钮
        self.create_master_controls(right_panel_layout)

        parent_layout.addWidget(right_panel)

    def create_connection_section(self, parent_layout):
        """创建连接区域"""
        connection_layout = QHBoxLayout()
        connection_layout.setContentsMargins(35, 0, 35, 0)  # 大胆增加左右边距，与控制框保持一致
        
        port_label = QLabel("Port:")
        self.port_combo = QComboBox()
        self.connect_button = QPushButton("Connect")
        self.connect_button.setObjectName("ConnectBtn")
        self.connect_button.clicked.connect(self.toggle_connection)
        
        connection_layout.addWidget(port_label)
        connection_layout.addWidget(self.port_combo, 1)
        connection_layout.addWidget(self.connect_button)
        
        parent_layout.addLayout(connection_layout)

    def create_first_control_box(self, parent_layout):
        """创建第一个控制框"""
        box = QGroupBox("主要控制")
        box.setObjectName("ControlBox")
        layout = QVBoxLayout(box)

        duty_layout, self.duty_ratio_input = self.create_control_item("Duty Ratio", "50.0", "%")
        layout.addLayout(duty_layout)
        
        flash_layout, self.flash_time_input = self.create_control_item("Flash Time", "100", "ms")
        layout.addLayout(flash_layout)

        parent_layout.addWidget(box)

    def create_second_control_box(self, parent_layout):
        """创建第二个控制框 (R-Ω & On/Off)"""
        box = QGroupBox("实时参数")
        box.setObjectName("ControlBox")
        layout = QVBoxLayout(box)
        layout.setSpacing(10)

        # 恢复 "R-Ω" 标签以匹配原始设计
        resistance_layout, self.resistance_display_widget = self.create_control_item("R-Ω", "---", "Ω", editable=False)
        layout.addLayout(resistance_layout)

        # 电阻测量的开关按钮，直接添加到垂直布局中以确保正确的对齐和尺寸
        self.measure_resistance_button = QPushButton("On/Off")
        self.measure_resistance_button.setCheckable(True)
        self.measure_resistance_button.setObjectName("ToggleButton")
        self.measure_resistance_button.toggled.connect(self.toggle_resistance_measurement)
        
        layout.addWidget(self.measure_resistance_button)
        parent_layout.addWidget(box)

    def create_control_item(self, label_text, value_text, unit_text, editable=True):
        """Creates a consistent layout for a control item (Label, Input, Unit)."""
        item_layout = QVBoxLayout()
        item_layout.setSpacing(8)
        
        # 标签
        label = QLabel(label_text)
        label.setObjectName("ControlLabel")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 值显示框和单位
        value_layout = QHBoxLayout()
        value_layout.setContentsMargins(0, 0, 0, 0)
        value_layout.addStretch()
        
        value_display = QLineEdit(value_text)
        value_display.setAlignment(Qt.AlignmentFlag.AlignCenter)
        value_display.setEnabled(editable)
        
        unit_label = QLabel(unit_text)
        unit_label.setObjectName("UnitLabel")
        
        value_layout.addWidget(value_display)
        value_layout.addWidget(unit_label)
        value_layout.addStretch()
        
        item_layout.addWidget(label)
        item_layout.addLayout(value_layout)
        
        return (item_layout, value_display)

    def create_master_controls(self, parent_layout):
        """创建主控制按钮"""
        parent_layout.addStretch()  # 推到底部
        
        controls_layout = QVBoxLayout()
        controls_layout.setContentsMargins(35, 0, 35, 0)  # 大胆增加左右边距，与其他控制元素保持一致
        controls_layout.setSpacing(10)
        
        self.master_off_button = QPushButton("Off")
        self.master_off_button.setObjectName("MasterOffBtn")
        self.master_off_button.clicked.connect(lambda: self.serial_thread.send_data(protocol.get_heat_off_msg()))
        
        self.master_on_button = QPushButton("On")
        self.master_on_button.setObjectName("MasterOnBtn")
        self.master_on_button.clicked.connect(lambda: self.serial_thread.send_data(protocol.get_heat_on_msg()))
        
        controls_layout.addWidget(self.master_off_button)
        controls_layout.addWidget(self.master_on_button)
        
        parent_layout.addLayout(controls_layout)

    def start_plotting(self):
        self.status_timer.stop() # Stop idle polling before attempting to plot
        self.append_to_log('[TIMER] status_timer stopped (start_plotting)')
        self.send_command_with_ack(
            protocol.get_start_plotting_msg,
            ack_code=b'AOK',
            success_callback=self._start_plotting_success,
            failure_callback=self._start_plotting_failure
        )

    def _start_plotting_success(self):
        self.clear_plot()
        self.plot_start_time = time.time()
        self.plot_timer.start(50)  # Start the correct timer for plotting
        self.append_to_log('[TIMER] plot_timer started (_start_plotting_success)')
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.show_status("Plotting started.")

    def _start_plotting_failure(self):
        """Called when starting the plot fails (ACK timeout)."""
        self.show_error("Failed to start plotting (AOK timeout).")
        self.start_button.setEnabled(True) # Re-enable start button
        self.stop_button.setEnabled(False)
        self.status_timer.start(1000) # Resume idle polling
        self.append_to_log('[TIMER] status_timer started (_start_plotting_failure)')
        
    def stop_plotting(self):
        self.plot_timer.stop() # Stop plot polling immediately
        self.append_to_log('[TIMER] plot_timer stopped (stop_plotting)')
        self.send_command_with_ack(
            protocol.get_stop_plotting_msg,
            ack_code=b'AOK',
            success_callback=self._stop_plotting_success,
            failure_callback=self._stop_plotting_failure
        )

    def _stop_plotting_success(self):
        self.status_timer.start(1000) # Resume idle polling now that plotting is stopped
        self.append_to_log('[TIMER] status_timer started (_stop_plotting_success)')
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.show_status("Plotting stopped.")

    def _stop_plotting_failure(self):
        """Called when stopping the plot fails (ACK timeout)."""
        self.show_error("Failed to stop plotting (AOK timeout).")
        # We are in an uncertain state, but let's try to restore the UI and polling
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_timer.start(1000)
        self.append_to_log('[TIMER] status_timer started (_stop_plotting_failure)')

    def clear_plot(self):
        """Clears the plot area and data"""
        self.time_data.clear()
        self.temp_data.clear()
        self.current_time = 0.0
        # 显式地用空数据更新图表，这是最可靠的清除方式
        self.plot_curve.setData(self.time_data, self.temp_data)
        self.status_timer.stop()
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)

    def update_plot(self, new_temps):
        """
        接收来自串口线程的数据并更新图表 (使用精确的累加时间)
        """
        if not self.plot_timer.isActive():
            return
            
        # 检查是否达到最大绘图时间 (基于累加的数据时间)
        if self.current_time >= self.max_plot_duration:
            self.stop_plotting()
            self.show_status(f"Max plot time ({self.max_plot_duration}s) reached. Plotting stopped.")
            return

        time_increment = 0.005 # 每个点代表5ms
        
        for temp in new_temps:
            self.time_data.append(self.current_time)
            self.temp_data.append(temp)
            self.current_time += time_increment
        
        # 限制显示的数据点数量以保持性能
        # 动态调整X轴范围，使其从0开始，而不是滚动
        if len(self.time_data) > 4000:
            self.plot_curve.setData(self.time_data[-4000:], self.temp_data[-4000:])
        else:
            self.plot_curve.setData(self.time_data, self.temp_data)

    def request_plot_data(self):
        if self._waiting_for_ack is not None:
            return # Don't send plot requests while waiting for an ACK
        self.serial_thread.send_data(protocol.get_request_plot_data_msg())

    def request_status(self):
        if self._waiting_for_ack is not None:
            return # Don't send status requests while waiting for an ACK
        self.serial_thread.send_data(protocol.get_request_status_msg())
        
    def update_status_display(self, status):
        # The new UI design updates status via individual controls and ACK messages.
        # This function can be used for other status updates if needed in the future.
        pass

    def update_resistance_display(self, value):
        self.resistance_display_widget.setText(f"{value / 10.0:.1f}")

    def set_duty_ratio(self):
        try:
            value_str = self.duty_ratio_input.text()
            value = int(float(value_str) * 10)
            self.send_command_with_ack(
                protocol.get_set_duty_ratio_msg, 
                value, 
                ack_code=b'COK'
            )
        except (ValueError, TypeError):
            self.show_error("Invalid Duty Ratio value.")

    def set_flash_time(self):
        try:
            value_str = self.flash_time_input.text()
            value = int(float(value_str) * 10)
            self.send_command_with_ack(
                protocol.get_set_flash_time_msg,
                value,
                ack_code=b'DOK'
            )
        except (ValueError, TypeError):
            self.show_error("Invalid Flash Time value.")

    def request_resistance_measurement(self):
        """Sends a request to measure resistance once."""
        self.send_command_with_ack(
            protocol.get_measure_resistance_on_msg,
            ack_code=b'BOK'
        )

    def toggle_resistance_measurement(self, checked):
        """处理电阻测量按钮的切换"""
        if checked:
            # Try to turn ON
            self.send_command_with_ack(
                protocol.get_measure_resistance_on_msg,
                ack_code=b'BOK',
                success_callback=self._start_resistance_measurement,
                failure_callback=lambda: self._revert_button_state(False) # Revert to OFF
            )
        else:
            # Try to turn OFF
            self.send_command_with_ack(
                protocol.get_measure_resistance_off_msg,
                ack_code=b'BOK',
                success_callback=self._stop_resistance_measurement,
                failure_callback=lambda: self._revert_button_state(True) # Revert to ON
            )

    def _start_resistance_measurement(self):
        """ACK received for starting measurement."""
        self.measure_resistance_button.setText("On/Off")
        self.resistance_timer.start(400) # Start polling every 400ms

    def _stop_resistance_measurement(self):
        """ACK received for stopping measurement."""
        self.resistance_timer.stop()
        self.measure_resistance_button.setText("On/Off")

    def _revert_button_state(self, checked):
        """在命令失败时安全地恢复按钮状态以避免递归。"""
        self.measure_resistance_button.blockSignals(True)
        self.measure_resistance_button.setChecked(checked)
        self.measure_resistance_button.blockSignals(False)
        self.measure_resistance_button.setText("On/Off")

    def update_port_list(self, ports):
        current_port = self.port_combo.currentText()
        self.port_combo.clear()
        if ports:
            self.port_combo.addItems(ports)
            self.show_status("Ready. Please select a port.")
        else:
            self.show_status("No serial ports found.")

    def toggle_connection(self):
        if not self.serial_thread.is_running:
            port_name = self.port_combo.currentText()
            if port_name:
                self.serial_thread.connect_port(port_name)
        else:
            self.serial_thread.disconnect_port()

    def on_connection_status_changed(self, is_connected):
        self.update_ui_for_connection(is_connected)
        self.connect_button.setText("Disconnect" if is_connected else "Connect")
        if is_connected:
            port_name = self.serial_thread.serial_port.port if hasattr(self.serial_thread, 'serial_port') and self.serial_thread.serial_port else self.port_combo.currentText()
            self.show_status(f"Connected to {port_name}.")
            self.status_timer.start(1000)
        else:
            self.show_status("Disconnected.")
            self.status_timer.stop()
        
    def update_ui_for_connection(self, is_connected):
        """Enable/disable UI elements based on connection status."""
        self.port_combo.setEnabled(not is_connected)
        self.start_button.setEnabled(is_connected)
        self.stop_button.setEnabled(False) # Stop should only be enabled when plotting
        self.clear_button.setEnabled(True) # Always enabled
        self.settings_button.setEnabled(True) # Always enabled
        
        # Controls in right panel
        self.duty_ratio_input.setEnabled(is_connected)
        self.flash_time_input.setEnabled(is_connected)
        self.measure_resistance_button.setEnabled(is_connected)
        self.master_on_button.setEnabled(is_connected)
        
        if not is_connected:
            self.connect_button.setText("Connect")
        else:
            self.connect_button.setText("Disconnect")

    def open_settings_dialog(self):
        """打开设置对话框，并传递当前设置"""
        current_settings = {
            "max_plot_time": int(self.max_plot_duration * 10),
            "min_temp": self.min_temp,
            "max_temp": self.max_temp
        }
        
        self.settings_dialog = SettingsDialog(
            current_settings=current_settings,
            parent=self
        )
        self.settings_dialog.settings_applied.connect(self.apply_settings)
        self.settings_dialog.exec()

    def apply_settings(self, settings):
        """应用从设置对话框传递过来的设置项"""
        self.max_plot_duration = settings['max_plot_duration']
        self.min_temp = settings['min_temp']
        self.max_temp = settings['max_temp']

        # 发送指令到下位机
        self.send_command_with_ack(protocol.get_set_max_plot_time_msg, int(self.max_plot_duration * 1000), ack_code=b'HOK')
        self.send_command_with_ack(protocol.get_set_t_min_msg, self.min_temp, ack_code=b'IOK')
        self.send_command_with_ack(protocol.get_set_t_max_msg, self.max_temp, ack_code=b'JOK')
        
        # 保存设置到 .ini 文件
        self.settings.setValue("max_plot_duration", self.max_plot_duration)
        self.settings.setValue("min_temp", self.min_temp)
        self.settings.setValue("max_temp", self.max_temp)

        self.show_status("Settings applied and saved.")

    def save_data(self):
        if not self.time_data:
            self.show_error("No data to save.")
            return
            
        filename, _ = QFileDialog.getSaveFileName(
            self, "Save Data", "", "CSV files (*.csv);;All files (*.*)"
        )
        
        if filename:
            try:
                with open(filename, 'w', newline='') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerow(['Time (s)', 'Temperature (C)'])
                    for time, temp in zip(self.time_data, self.temp_data):
                        writer.writerow([time, temp])
                self.show_status(f"Data saved to {filename}")
            except Exception as e:
                self.show_error(f"Failed to save data: {str(e)}")

    def open_data_file(self):
        """Opens a CSV file and plots the historical data."""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Open History File",
            "",  # Start directory
            "CSV Files (*.csv);;All Files (*)"
        )

        if not file_path:
            return  # User cancelled

        time_data = []
        temp_data = []
        possible_encodings = ['utf-8', 'gbk', 'utf-8-sig', 'latin-1']
        file_opened_successfully = False

        for encoding in possible_encodings:
            try:
                with open(file_path, 'r', newline='', encoding=encoding) as csvfile:
                    reader = csv.reader(csvfile)
                    header = next(reader)

                    if header != ['Time (s)', 'Temperature (C)']:
                        self.show_error("Invalid file format or header.")
                        return

                    # Reset data for each attempt to avoid partial reads from wrong encoding
                    time_data = []
                    temp_data = []
                    for row in reader:
                        if len(row) == 2:
                            time_data.append(float(row[0]))
                            temp_data.append(float(row[1]))
                
                file_opened_successfully = True
                break  # Exit loop on success
            except UnicodeDecodeError:
                continue  # Try next encoding
            except Exception as e:
                self.show_error(f"Failed to parse file: {e}")
                return

        if not file_opened_successfully:
            self.show_error("Failed to open file. Unsupported encoding.")
            return

        if not time_data:
            self.show_error("No data found in the file.")
            return

        self.clear_plot()
        self.time_data = time_data
        self.temp_data = temp_data
        self.plot_curve.setData(self.time_data, self.temp_data)
        self.plot_widget.autoRange()

        self.show_status(f"Loaded data from {file_path.split('/')[-1]}")

    def show_status(self, message):
        self.status_label.setText(message)

    def show_error(self, message):
        self.status_label.setText(f"Error: {message}")

    def closeEvent(self, event):
        """确保在关闭应用前保存所有设置"""
        # 终极保险：在关闭前再次强制同步设置
        self.settings.sync()

        # 正常关闭线程和端口
        if self.serial_thread.is_running and self.serial_thread.is_connected:
            self.serial_thread.disconnect_port()
        
        self.serial_thread.quit()
        if not self.serial_thread.wait(1000): # Wait up to 1s
             self.serial_thread.terminate() # Force terminate if it doesn't quit
        
        self.plot_timer.stop()
        self.status_timer.stop()
        self.resistance_timer.stop()
        self.ack_timer.stop()
        event.accept()

    # --- Command Sending and ACK Handling ---
    
    def send_command_with_ack(self, command_func, *args, ack_code, success_callback=None, failure_callback=None):
        """
        Sends a command and waits for a specific ACK.
        """
        if self._waiting_for_ack is not None:
            # More informative error message
            current_waiting_ack = self._waiting_for_ack.decode(errors='ignore')
            self.show_error(f"Cannot send new command, already waiting for '{current_waiting_ack}'.")
            if failure_callback:
                failure_callback()
            return
            
        self._waiting_for_ack = ack_code
        self._ack_success_callback = success_callback
        self._ack_failure_callback = failure_callback

        command = command_func(*args)
        self.serial_thread.send_data(command)
        
        self.show_status(f"Sending command... waiting for: {ack_code.decode()}")
        self.ack_timer.start(2000) # 2-second timeout

    def handle_command_ack(self, ack_code):
        """Handles the command_ack_received signal from the serial thread."""
        if self.ack_timer.isActive() and ack_code == self._waiting_for_ack:
            self.ack_timer.stop()
            
            # 使用ACK_MESSAGES字典来获取用户友好的消息
            success_message = ACK_MESSAGES.get(ack_code, f"命令 {ack_code.decode()} 成功")
            self.show_status(success_message)
            
            if self._ack_success_callback:
                self._ack_success_callback()

            # 重置状态
            self._waiting_for_ack = None
            self._ack_success_callback = None
            self._ack_failure_callback = None

    def on_ack_timeout(self):
        """ACK等待超时处理"""
        if self._waiting_for_ack:
            self.show_error(f"错误: 等待 {self._waiting_for_ack.decode()} 超时！")
            if self._ack_failure_callback:
                self._ack_failure_callback()

            # 重置状态
            self._waiting_for_ack = None
            self._ack_success_callback = None
            self._ack_failure_callback = None

    def create_log_viewer(self, parent_layout):
        """Creates the communication log viewer text area."""
        self.log_group_box = QGroupBox("通信日志")
        log_layout = QVBoxLayout()

        self.log_viewer = QTextEdit()
        self.log_viewer.setReadOnly(True)
        self.log_viewer.setStyleSheet(
            "QTextEdit { "
            "  background-color: #2c313c; "
            "  color: #d0d0d0; "
            "  font-family: Consolas, 'Courier New', monospace; "
            "  border: 1px solid #454545;"
            "}"
        )
        log_layout.addWidget(self.log_viewer)
        self.log_group_box.setLayout(log_layout)
        parent_layout.addWidget(self.log_group_box)

    def append_to_log(self, message: str):
        """Appends a message to the log viewer and ensures it's visible."""
        self.log_viewer.append(message)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    main_win = MainWindow()
    main_win.show()
    sys.exit(app.exec())
